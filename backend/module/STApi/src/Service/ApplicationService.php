<?php

declare(strict_types=1);

namespace STApi\Service;

use STApi\Data\ApiApplicationsTable;
use ST<PERSON>pi\Entity\Application;
use STCompany\Entity\Company;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class ApplicationService
{
    use BaseHydratorTrait;

    /**
     *
     * @var ApiApplicationsTable
     */
    protected ApiApplicationsTable $apiApplicationsTable;

    /**
     *
     * @param ApiApplicationsTable $apiApplicationsTable
     */
    public function __construct(ApiApplicationsTable $apiApplicationsTable)
    {
        $this->apiApplicationsTable = $apiApplicationsTable;
    }

    /**
     *
     * @param string $token
     * @return Application|null
     */
    public function getApplicationByToken(string $token): ?Application
    {
        $applicationData = $this->apiApplicationsTable->getApplicationByToken($token);
        if (is_null($applicationData)) {
            return null;
        }

        return $this->hydrate($applicationData->getArrayCopy(), Application::class);
    }

    /**
     *
     * @param Company $company
     * @return Application
     */
    public function generateApplication(Company $company): Application
    {
        $application = $this->hydrate([
            'name' => $company->getName(),
            'type' => ApiApplicationsTable::THIRD_PARTY_COMPANY_APPLICATION,
            'company_id' => $company->getId(),
            'token' => sha1(uniqid()),
        ], Application::class);
        $this->apiApplicationsTable->saveApplication($application);
        return $application;
    }
}
