<?php

declare(strict_types=1);

namespace STCompany;

use Laminas\Di\Container\ServiceManager\AutowireFactory;
use STAlgo\Data\AlgoApiIndustriesTable;
use STCall\Service\Export\Interfaces\CompaniesToExportSelectorInterface as CallCompaniesToExportSelectorInterface;
use STCall\Service\Interfaces\CompanySaverInterface as CallCompanySaverInterface;
use STCall\Service\Interfaces\CompanySelectorInterface as CallCompanySelectorInterface;
use STCall\Service\Interfaces\TeamSelectorInterface;
use STCall\Service\Interfaces\UserTeamIdsSelectorInterface as CallUserTeamIdsSelectorInterface;
use STCall\Service\Interfaces\RolesSelectorInterface as CallRolesSelectorInterface;
use STCompany\Data\CompaniesWebhooksSettingsTable;
use STCompany\Data\CompanyIndustriesTable;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Service\LlmEvent\LlmEventSelectorService;
use STCompany\ServiceProviding\CompanyServiceProvider;
use STIndustry\Data\IndustriesLlmEventsTable;
use STLib\Mvc\Data\HydratedTableFactory;
use STLib\Mvc\Data\TableFactory;
use STLib\Mvc\DependencyInjection\DefaultFactory;
use STOnboarding\Data\OnboardingFormsTable;
use STOnboarding\Service\Interfaces\CompanyCreatorInterface as OnboardingCompanyCreatorInterface;
use STOnboarding\Service\Interfaces\EventsAlgoEventsSelectorInterface as OnboardingEventsAlgoEventsSelectorInterface;

return [
    'controller_plugins' => [
        'invokables' => [
            'company' => Controller\Plugin\Company::class,
            'companyPermissionChecker' => Controller\Plugin\CompanyPermissionChecker::class,
            'teamPermissionChecker' => Controller\Plugin\TeamPermissionChecker::class,
            'userNotification' => Controller\Plugin\UserNotification::class,
        ],
    ],
    'service_manager' => [
        'factories' => [
            Data\CompaniesTable::class => TableFactory::class,
            Data\CompanyDataRemoveRepository::class => \STClickhouse\Entity\TableFactory::class,
            Data\CompaniesCallTemplatesTable::class => TableFactory::class,
            Data\CompaniesLanguagesTable::class => TableFactory::class,
            Data\CompaniesRatesTable::class => TableFactory::class,
            Data\CompaniesVocabularyWordsTable::class => TableFactory::class,
            Data\RolesTable::class => TableFactory::class,
            Data\PermissionsTable::class => TableFactory::class,
            Data\RolesPermissionsTable::class => TableFactory::class,
            Data\TeamsTable::class => TableFactory::class,
            Data\UsersCompaniesRolesTable::class => TableFactory::class,
            Data\UsersTeamsTable::class => TableFactory::class,
            Data\UsersCompaniesLanguagesTable::class => TableFactory::class,
            Data\EventsTable::class => HydratedTableFactory::class,
            Data\ChecklistsTable::class => HydratedTableFactory::class,
            Data\ChecklistsPointsTable::class => HydratedTableFactory::class,
            Data\EventsSearchWordsTable::class => TableFactory::class,
            Data\EventsColorsTable::class => TableFactory::class,
            Data\EventsAlgoEventsTable::class => TableFactory::class,
            Data\EventsCategoriesTable::class => TableFactory::class,
            Data\CompanyDetailsTable::class => HydratedTableFactory::class,
            Data\ClientsTable::class => \STClickhouse\Entity\TableFactory::class,
            Data\PrecalculatedClientsTable::class => \STClickhouse\Entity\TableFactory::class,
            Data\PrecalculatedAgentsTable::class => \STClickhouse\Entity\TableFactory::class,
            Data\UsersNotificationsTable::class => TableFactory::class,
            Data\UsersNotificationsCallEventsTable::class => TableFactory::class,
            Service\CompanyService::class => Service\CompanyServiceFactory::class,
            Service\CompanyDataRemoveService::class => DefaultFactory::class,
            Service\RoleService::class => DefaultFactory::class,
            Service\PermissionService::class => DefaultFactory::class,
            Service\EventService::class => DefaultFactory::class,
            Service\TeamService::class => DefaultFactory::class,
            Service\CompanyAvatarService::class => DefaultFactory::class,
            Service\ClientService::class => DefaultFactory::class,
            Service\ClientPrecalculation\ClientPrecalculationService::class => DefaultFactory::class,
            Service\ClientPrecalculation\ClientPrecalculationManagerService::class => DefaultFactory::class,
            Service\AgentPrecalculation\AgentPrecalculationService::class => DefaultFactory::class,
            Service\AgentPrecalculation\AgentPrecalculationManagerService::class => DefaultFactory::class,
            Service\Notification\UserNotificationService::class => DefaultFactory::class,
            Service\Notification\UserNotificationSenderService::class => DefaultFactory::class,
            Validator\TeamValidator::class => Validator\TeamValidatorFactory::class,
            Validator\RoleValidator::class => Validator\RoleValidatorFactory::class,
            Validator\EventValidator::class => Validator\EventValidatorFactory::class,
            Validator\EventCategoryValidator::class => Validator\EventCategoryValidatorFactory::class,
            Validator\CompanyVocabularyWordValidator::class => Validator\CompanyVocabularyWordValidatorFactory::class,
            LlmEventSelectorService::class => AutowireFactory::class,
            CompanyLlmEventsTable::class => HydratedTableFactory::class,
            CompanyIndustriesTable::class => HydratedTableFactory::class,
            AlgoApiIndustriesTable::class => HydratedTableFactory::class,
            IndustriesLlmEventsTable::class => HydratedTableFactory::class,
            OnboardingFormsTable::class => HydratedTableFactory::class,
            CompaniesWebhooksSettingsTable::class => TableFactory::class
        ],
        'aliases' => [
            OnboardingEventsAlgoEventsSelectorInterface::class => CompanyServiceProvider::class,
            OnboardingCompanyCreatorInterface::class => CompanyServiceProvider::class,
            CallCompanySelectorInterface::class => CompanyServiceProvider::class,
            CallCompaniesToExportSelectorInterface::class => CompanyServiceProvider::class,
            CallCompanySaverInterface::class => CompanyServiceProvider::class,
            TeamSelectorInterface::class => CompanyServiceProvider::class,
            CallUserTeamIdsSelectorInterface::class => CompanyServiceProvider::class,
            CallRolesSelectorInterface::class => CompanyServiceProvider::class,
        ],
    ],
];
