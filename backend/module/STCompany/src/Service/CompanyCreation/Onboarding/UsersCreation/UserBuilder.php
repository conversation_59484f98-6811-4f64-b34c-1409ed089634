<?php

declare(strict_types=1);

namespace STCompany\Service\CompanyCreation\Onboarding\UsersCreation;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Entity\Company;
use STCompany\Entity\Role;
use STCompany\Entity\Team;
use STCompany\Entity\User as CompanyUser;
use STCompany\Service\Interfaces\UserSelectorInterface;
use STCompany\Service\RoleService;
use STUser\Entity\User;

class UserBuilder
{
    public const string NEW_TEAM_NAME = 'New Team';

    public function __construct(
        private readonly UserSelectorInterface $userSelector,
        private readonly RoleService $roleService,
    ) {
    }

    public function buildTeam(Company $company): Team
    {
        $team = new Team();
        $team->setCompanyId($company->getId());
        $team->setName(self::NEW_TEAM_NAME);

        return $team;
    }

    /**
     * @throws NotFoundApiException
     */
    public function buildUser(string $name, string $email, Company $company, Team $team): CompanyUser
    {
        $user = new CompanyUser();
        $user->setEmail($email);

        $existedUser = $this->searchExistedUser($email);
        if ($existedUser) {
            $user->setId($existedUser->getId());
            $user->setName($existedUser->getName());
            $user->setAvatar($existedUser->getAvatar());
            $user->setTwoFactorSecret($existedUser->getTwoFactorSecret());
            $user->setRegistrationDate($existedUser->getRegistrationDate());
            $user->isFirstLogin($existedUser->isFirstLogin());
        } else {
            $user->setName($name);
            $user->initRegistrationDate();
            $user->isFirstLogin(true);
        }

        $role = new Role();
        $role->setId($this->roleService->getRoleIdByType(Role::COMPANY_ADMIN_ROLE_TYPE, $company->getId()));
        $role->setCompanyId($company->getId());

        $user->setRole($role);
        $user->getTeams()->add($team);

        return $user;
    }

    private function searchExistedUser(string $email): ?User
    {
        try {
            return $this->userSelector->getUserByEmail($email);
        } catch (NotFoundApiException) {
            return null;
        }
    }
}
