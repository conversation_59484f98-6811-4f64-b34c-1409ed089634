<?php

declare(strict_types=1);

namespace STEms\Data;

use Carbon\Carbon;
use STApi\Entity\Exception\NotFoundApiException;

class EmsDataSetsTable extends \STClickhouse\Entity\BaseTable
{
    use \STClickhouse\Data\QueriesTrait;
    use \STClickhouse\Data\CallsWhereCondition;

    public const NOT_SENT_TO_TRAIN = 'not_sent_to_train';
    public const SENT_TO_TRAIN = 'sent_to_train';

    /**
     * @param string $dataSetId
     * @param ?int $companyId
     * @return array|null
     */
    public function getDataSet(string $dataSetId, ?int $companyId): ?array
    {
        $getFinalTableWhereCondition = [
            'data_set_id' => $dataSetId,
        ];

        if ($companyId !== null) {
            $getFinalTableWhereCondition['company_id'] = $companyId;
        }

        $sql = <<<SQL
            SELECT
                *
            FROM ({$this->getFinalTableSqlUsingGroupBy(
                'ems_data_sets',
                [
                    'data_set_id',
                ],
                'updated_at',
                [
                    'event_id',
                    'guideline',
                    'name',
                    'status',
                    'reviewed_calls_examples_last_update',
                    'analyzed_calls_examples_last_update',
                    'positive_example_text',
                    'positive_example_highlight',
                    'negative_example_text',
                    'company_id',
                    'user_id',
                    'created_at',
                    'updated_at',
                    'is_deleted',
                ],
                $getFinalTableWhereCondition
            )}) eds
            WHERE
                eds.is_deleted = 0
        SQL;

        $dataSetData = $this->getClient()->selectOne($sql);

        if ($dataSetData === null) {
            throw new NotFoundApiException('Data Set is not found');
        }

        return $dataSetData;
    }

    /**
     * @param int $eventId
     * @param int $companyId
     * @return array|null
     */
    public function getDataSetByEventId(int $eventId, int $companyId): ?array
    {
        $sql = <<<SQL
            SELECT
                *
            FROM ({$this->getFinalTableSqlUsingGroupBy(
                'ems_data_sets',
                [
                    'data_set_id',
                ],
                'updated_at',
                [
                    'event_id',
                    'guideline',
                    'name',
                    'status',
                    'reviewed_calls_examples_last_update',
                    'positive_example_text',
                    'positive_example_highlight',
                    'negative_example_text',
                    'company_id',
                    'user_id',
                    'created_at',
                    'updated_at',
                    'is_deleted',
                ],
                [
                    'event_id' => $eventId,
                    'company_id' => $companyId,
                ]
            )}) eds
            WHERE
                eds.is_deleted = 0
        SQL;

        return $this->getClient()->selectOne($sql);
    }

    /**
     * @param array $eventsIds
     * @return array
     */
    public function getDataSetsIdsByEventsIds(array $eventsIds): array
    {
        $sql = <<<SQL
            SELECT
                eds.data_set_id data_set_id
            FROM
                ({$this->getFinalTableSqlUsingGroupBy(
                'ems_data_sets',
                [
                    'data_set_id',
                ],
                'updated_at',
                [
                    'is_deleted',
                ],
                [
                    [
                        'type' => 'compare',
                        'column' => 'event_id',
                        'value' => $eventsIds,
                        'compare' => 'IN',
                    ],
                ]
            )}) eds
            WHERE
                eds.is_deleted = 0
        SQL;

        return $this->getClient()->selectColumn($sql, 'data_set_id');
    }

    /**
     * @param \STEms\Entity\DataSet $dataSet
     * @return void
     */
    public function saveDataSet(\STEms\Entity\DataSet $dataSet): void
    {
        $data = $dataSet->toArray();

        $data['updated_at'] = new Carbon();
        $data['is_deleted'] = (int) $data['is_deleted'];

        $this->getClient()->insert(
            $this->getTableName(),
            [$data],
            array_keys($data)
        );
    }
}
