<?php

declare(strict_types=1);

namespace STEms\Service;

use Carbon\Carbon;
use STApi\Entity\Exception\NotFoundApiException;
use STEms\Data\EmsDataSetExamplesEventsTable;
use STEms\Data\EmsDataSetExamplesTable;
use STEms\Data\EmsDataSetsTable;
use STEms\DTO\DataSetExampleParagraphsTextDTO;
use STEms\Entity\DataSetExampleCollection;
use STEms\Entity\DataSetExample;
use STEms\Entity\DataSetExampleEvent;
use STEms\Entity\DataSetExampleEventCollection;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class EmsDataSetExampleService
{
    use BaseHydratorTrait;

    public const EXAMPLE_MIN_LENGTH = 500;
    public const CUSTOM_EXAMPLE_MIN_LENGTH = 100;
    public const MINIMAL_CONFIRMED_EXAMPLES_COUNT_TO_CONFIRM = 100;
    public const MINIMAL_NEUTRAL_EXAMPLES_COUNT_TO_CONFIRM = 30;

    /**
     *
     * @param EmsDataSetExamplesTable $emsDataSetExamplesTable
     * @param EmsDataSetExamplesEventsTable $emsDataSetExampleEventsTable
     * @param EmsDataSetsTable $emsDataSetsTable
     */
    public function __construct(
        protected EmsDataSetExamplesTable $emsDataSetExamplesTable,
        protected EmsDataSetExamplesEventsTable $emsDataSetExampleEventsTable,
        protected EmsDataSetsTable $emsDataSetsTable,
    ) {
    }

    /**
     * @param string $dataSetId
     * @param string|array|null $status
     * @param bool $isRemoved
     * @return DataSetExampleCollection
     * @throws \ReflectionException
     */
    public function getDataSetExamples(string $dataSetId, string|array $status = null, bool $isRemoved = false): DataSetExampleCollection
    {
        $dataSetExamplesCollection = new DataSetExampleCollection();

        foreach ($this->emsDataSetExamplesTable->getDataSetExamples($dataSetId, $status, $isRemoved) as $dataSetExampleData) {
            $dataSetExampleEventsCollection = new DataSetExampleEventCollection();

            foreach ($dataSetExampleData['events'] as $dataSetExampleEventsData) {
                $dataSetExampleEventsData['is_deleted'] = $dataSetExampleEventsData['is_deleted'] === 'true';
                $dataSetExampleEventsCollection->add($this->hydrate($dataSetExampleEventsData, DataSetExampleEvent::class));
            }

            $dataSetExampleData['events'] = $dataSetExampleEventsCollection;
            $dataSetExamplesCollection->add($this->hydrate($dataSetExampleData, DataSetExample::class));
        }

        return $dataSetExamplesCollection;
    }

    /**
     * @param string $dataSetExampleId
     * @param int $companyId
     * @param bool $isRemoved
     * @return DataSetExample
     * @throws NotFoundApiException
     * @throws \ReflectionException
     */
    public function getDataSetExample(string $dataSetExampleId, int $companyId, bool $isRemoved = false): DataSetExample
    {
        $dataSetExampleData = $this->emsDataSetExamplesTable->getDataSetExample($dataSetExampleId, $companyId, $isRemoved);

        /** @var DataSetExample $dataSetExample */
        $dataSetExample = $this->hydrate(
            $dataSetExampleData,
            DataSetExample::class,
        );

        $dataSetExample->setEvents($this->getDataSetExampleEvents($dataSetExample->getDataSetExampleId()));

        return $dataSetExample;
    }

    /**
     * @param string $dataSetExampleEventId
     * @return DataSetExampleEvent
     * @throws NotFoundApiException
     */
    public function getDataSetExampleEvent(string $dataSetExampleEventId): DataSetExampleEvent
    {
        $dataSetExampleEventData = $this->emsDataSetExampleEventsTable->getDataSetExampleEvent($dataSetExampleEventId);

        /** @var DataSetExampleEvent $dataSetExampleEvent */
        $dataSetExampleEvent = $this->hydrate(
            $dataSetExampleEventData,
            DataSetExampleEvent::class,
        );

        return $dataSetExampleEvent;
    }

    /**
     * @param string $dataSetExampleId
     * @return DataSetExampleEventCollection
     */
    public function getDataSetExampleEvents(string $dataSetExampleId): DataSetExampleEventCollection
    {
        $dataSetExampleEventsData = $this->emsDataSetExampleEventsTable->getDataSetExampleEvents($dataSetExampleId);
        $dataSetExampleEventsCollection = new DataSetExampleEventCollection();

        foreach ($dataSetExampleEventsData as $dataSetExampleEventData) {
            $dataSetExampleEventsCollection->add($this->hydrate($dataSetExampleEventData, DataSetExampleEvent::class));
        }

        return $dataSetExampleEventsCollection;
    }

    /**
     * @param array $dataSetIds
     * @param array|null $statuses
     * @return array
     */
    public function exportDataSetExamples(array $dataSetIds, ?array $statuses = null): array
    {
        return $this->emsDataSetExamplesTable->exportDataSetExamples(
            $dataSetIds,
            $statuses
        );
    }

    /**
     * @param string $dataSetId
     * @param string $status
     * @return int
     */
    public function getExamplesCount(string $dataSetId, string $status): int
    {
        return $this->emsDataSetExamplesTable->getExamplesCount($dataSetId, $status);
    }

    /**
     * @param DataSetExample $dataSetExample
     * @return void
     */
    public function saveDataSetExample(DataSetExample $dataSetExample): void
    {
        $dataSetExample->setUpdatedAt(new Carbon());
        $this->emsDataSetExamplesTable->saveDataSetExample($dataSetExample);
    }

    /**
     * @param DataSetExampleEvent $dataSetExampleEvent
     * @return void
     */
    public function saveDataSetExampleEvent(DataSetExampleEvent $dataSetExampleEvent): void
    {
        $dataSetExampleEvent->setUpdatedAt(new Carbon());
        $this->emsDataSetExampleEventsTable->saveDataSetExampleEvent($dataSetExampleEvent);
    }

    /**
     * @param DataSetExampleCollection $dataSetExampleCollection
     * @return void
     */
    public function saveDataSetExamples(DataSetExampleCollection $dataSetExampleCollection): void
    {
        $this->emsDataSetExamplesTable->saveDataSetExamples($dataSetExampleCollection);

        $dataSetExamplesEventsCollection = new DataSetExampleEventCollection();

        /** @var DataSetExample $dataSetExample */
        foreach ($dataSetExampleCollection as $dataSetExample) {
            foreach ($dataSetExample->getEvents() as $dataSetExampleEvent) {
                $dataSetExamplesEventsCollection->add($dataSetExampleEvent);
            }
        }

        $this->emsDataSetExampleEventsTable->saveDataSetExamplesEvents($dataSetExamplesEventsCollection);
    }

    /**
     * @param array $callEvents
     * @param string $dataSetId
     * @return DataSetExampleCollection
     */
    public function makeDataSetExamplesFromEvents(array $callEvents, string $dataSetId): DataSetExampleCollection
    {
        $dataSetExamplesCollection = new DataSetExampleCollection();

        if (!empty($callEvents)) {
            foreach ($callEvents as $event) {
                $callParagraphs = $event['paragraphs'];
                $callParagraphsEn = $event['paragraphs_en'];
                $eventParagraphNumber = $event['paragraph_number'];
                $exampleEvents = new DataSetExampleEventCollection();
                $dataSetExampleId = uniqid('', true);

                $paragraphsTexts = $this->combineParagraphs(
                    $callParagraphs,
                    $eventParagraphNumber
                );
                $paragraphsTextsEn = $this->combineParagraphs(
                    $callParagraphsEn,
                    $eventParagraphNumber
                );

                if (isset($event['event_is_deleted'])) {
                    $exampleSource = $event['event_is_deleted'] === true
                        ? DataSetExample::EXAMPLE_SOURCE_NEUTRAL
                        : DataSetExample::EXAMPLE_SOURCE_CONFIRMED;
                } else {
                    $exampleSource = $event['example_source'];
                }

                $exampleTextParagraphNumbers = array_unique(
                    array_merge(
                        $paragraphsTexts->paragraphsNumbers,
                        $paragraphsTextsEn->paragraphsNumbers
                    )
                );

                foreach ($exampleTextParagraphNumbers as $paragraphNumber) {
                    foreach ($event['additional_events'] as $additionalEvent) {
                        if ((string) $paragraphNumber === $additionalEvent['paragraph']) {
                            $exampleEvents->add(
                                $this->hydrate(
                                    [
                                        'data_set_example_event_id' => uniqid('', true),
                                        'data_set_example_id' => $dataSetExampleId,
                                        'event_id' => $additionalEvent['event_id'],
                                        'highlight' => (string) $additionalEvent['event_highlight'],
                                        'en_highlight' => $additionalEvent['event_en_highlight'],
                                    ],
                                    DataSetExampleEvent::class
                                )
                            );
                        }
                    }
                }

                $dataSetExamplesCollection->add(
                    $this->hydrate(
                        [
                            'data_set_id' => $dataSetId,
                            'data_set_example_id' => $dataSetExampleId,
                            'text' => $paragraphsTexts->text,
                            'status' => EmsDataSetExamplesTable::STATUS_UNSORTED,
                            'en_text' => $paragraphsTextsEn->text,
                            'paragraph_number' => $event['paragraph_number'],
                            'language' => $event['language'],
                            'call_id' => $event['call_id'],
                            'example_source' => $exampleSource,
                            'events' => $exampleEvents,
                            'paragraph_start_time' => (int) $event['paragraph_start_time'],
                        ],
                        DataSetExample::class
                    )
                );
            }
        }

        return $dataSetExamplesCollection;
    }

    /**
     * If paragraph length less than 500 characters we combine it with neighboring paragraphs
     *
     * @param array $paragraphTexts
     * @param int $paragraphNumber
     * @param int $minLength
     * @return DataSetExampleParagraphsTextDTO
     */
    private function combineParagraphs(
        array $paragraphTexts,
        int $paragraphNumber,
        int $minLength = EmsDataSetExampleService::EXAMPLE_MIN_LENGTH,
    ): DataSetExampleParagraphsTextDTO {
        $combinedText = $paragraphTexts[$paragraphNumber] ?? '';
        $paragraphsNumbers[] = $paragraphNumber;

        $paragraphOffset = 1;
        while (
            mb_strlen($combinedText) < $minLength
            &&
            (
                isset($paragraphTexts[$paragraphNumber - $paragraphOffset])
                || isset($paragraphTexts[$paragraphNumber + $paragraphOffset])
            )
        ) {
            if (isset($paragraphTexts[$paragraphNumber - $paragraphOffset])) {
                $combinedText = $paragraphTexts[$paragraphNumber - $paragraphOffset] . "\n" . $combinedText;
                $paragraphsNumbers[] = $paragraphNumber - $paragraphOffset;
            }
            if (isset($paragraphTexts[$paragraphNumber + $paragraphOffset])) {
                $combinedText .= "\n" . $paragraphTexts[$paragraphNumber + $paragraphOffset];
                $paragraphsNumbers[] = $paragraphNumber + $paragraphOffset;
            }
            $paragraphOffset++;
        }

        return new DataSetExampleParagraphsTextDTO(
            $combinedText,
            $paragraphsNumbers,
        );
    }

    /**
     * @param string $dataSetId
     * @param array|null $statuses
     * @return void
     */
    public function deleteDataSetExamples(string $dataSetId, ?array $statuses): void
    {
        $dataSetExamples = $this->getDataSetExamples($dataSetId, $statuses);

        /** @var DataSetExample $dataSetExample */
        foreach ($dataSetExamples as $dataSetExample) {
            $dataSetExample->isDeleted(true);
            $dataSetExample->setEvents(new DataSetExampleEventCollection());
            $dataSetExample->setUpdatedAt(new Carbon());
        }

        $this->saveDataSetExamples($dataSetExamples);
    }

    /**
     * @param string $dataSetId
     * @param array|null $statuses
     * @return void
     */
    public function restoreDataSetExamples(string $dataSetId, ?array $statuses): void
    {
        $dataSetExamples = $this->getDataSetExamples($dataSetId, $statuses, true);

        /** @var DataSetExample $dataSetExample */
        foreach ($dataSetExamples as $dataSetExample) {
            $dataSetExample->isDeleted(false);
            $dataSetExample->setUpdatedAt(new Carbon());
        }

        $this->saveDataSetExamples($dataSetExamples);
    }
}
