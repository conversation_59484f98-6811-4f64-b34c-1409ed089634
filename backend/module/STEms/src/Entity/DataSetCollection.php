<?php

declare(strict_types=1);

namespace STEms\Entity\CalculatedEvent;

use STEms\Entity\DataSet;
use STLib\Expand\Collection;

class DataSetCollection extends \STLib\Expand\Collection
{
    /**
     *
     * @param mixed $dataSet
     * @param string|int|null $key
     * @return Collection
     */
    public function add(mixed $dataSet, string|int|null $key = null): \STLib\Expand\Collection
    {
        if (!($dataSet instanceof DataSet)) {
            throw new \RuntimeException('Call must be an instance of "\STEms\Entity\DataSet"');
        }
        parent::add($dataSet, $key ?? $dataSet->getEmsDataSetId());
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach ($this as $dataSet) {
            $result[] = $dataSet->toArray();
        }
        return $result;
    }
}
