<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use STAlgo\Service\AiSolutionsCommutatorService;
use STCall\Data\CallsTable;
use STCall\Entity\Call;
use ST<PERSON>all\Entity\CallFactory;
use ST<PERSON>all\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\CallAnalysis\Exception\StepIsFailedWithErrorFromAlgoModelException;
use STCall\Service\CallAnalysisService;
use STCall\Service\CallChecklistService;
use STCall\Service\CallService;
use STCall\Service\EventTriggerService;
use STCall\Service\Interfaces\TeamSelectorInterface;
use STCompany\Entity\Checklist\Checklist;
use STCompany\Entity\Company;
use STCompany\Entity\Team;
use STCompany\Service\Checklist\ChecklistPointService;
use STCompany\Service\Checklist\ChecklistService;
use STCompany\Service\CompanyService;
use STRabbit\Service\ProvidesRabbit;

class ChecklistStep extends BaseStep
{
    use ProvidesRabbit;

    public const string CALL_CHECKLIST_QUEUE = 'call-checklist-step';
    public const string CALL_CHECKLIST_ERROR_QUEUE = 'call-checklist-step-error';

    public function __construct(
        protected CallService $callService,
        protected CallsTable $callsTable,
        protected CallChecklistService $callChecklistService,
        protected ChecklistService $checklistService,
        protected ChecklistPointService $checklistPointService,
        protected CallFactory $callFactory,
        protected CompanyService $companyService,
        protected TeamSelectorInterface $teamSelector,
        protected AiSolutionsCommutatorService $aiSolutionsCommutatorService,
        private readonly EventTriggerService $eventTrigger
    ) {
        parent::__construct($callsTable, $callFactory);
    }

    /**
     *
     * @return string
     */
    public function getQueueName(): string
    {
        return static::CALL_CHECKLIST_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string
    {
        return static::CALL_CHECKLIST_ERROR_QUEUE;
    }

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string
    {
        return SummarizationStep::CALL_SUMMARIZATION_QUEUE;
    }

    /**
     *
     * @param int $companyId
     * @return bool
     * @throws StepIsAlreadyFinishedException
     * @throws \ReflectionException
     * @throws StepIsFailedWithErrorFromAlgoModelException
     */
    public function run(int $companyId): bool
    {
        $call = $this->getCall();

        if ($call->isChecklistCompleted()) {
            throw new Exception\StepIsAlreadyFinishedException('Checklist step is finished');
        }

        $company = $this->companyService->getCompany($companyId);

        if (!$company->isChecklistsEnabled()) {
            $this->markStepAsCompleted($call);
            return true;
        }

        $checklists = $this->checklistService->getCompanyChecklists($company->getId());
        $paragraphCollection = $this->callService->getParagraphs(
            $company,
            $call->getId(),
        );

        $data = [];
        foreach ($checklists as $checklist) {
            if (!$this->shouldRunChecklist($call, $company, $checklist)) {
                continue;
            }

            $checklistPoints = $this->checklistPointService->getChecklistPointsByChecklistId(
                $checklist->getId(),
                $companyId
            );

            if ($checklistPoints->isEmpty()) {
                continue;
            }

            $checklistResult = $this->aiSolutionsCommutatorService->getChecklistResult(
                $call,
                $paragraphCollection,
                $checklistPoints,
                $this->companyService->getCompanyDetails($company->getId())
            );

            if (!$checklistResult->isSuccessful()) {
                throw new Exception\StepIsFailedWithErrorFromAlgoModelException();
            }

            $this->callChecklistService->saveCallChecklistPointsResults(
                $call,
                $checklistPoints,
                $checklistResult
            );

            $checklistPointsData = [];
            foreach ($checklistPoints as $checklistPoint) {
                $checklistPointsData[] = $checklistPoint->toArray();
            }

            $data[] = [
                'checklist' => $checklist->toArray(),
                'checklist_points' => $checklistPointsData,
                'checklist_result' => $checklistResult->getChecklistResult(),
            ];
        }

        $this->markStepAsCompleted($call);

        $this->triggerStepFinishedEvent($call->getId(), $companyId, $data);

        return true;
    }

    private function markStepAsCompleted(Call $call): void
    {
        $call->isChecklistCompleted(true);
        $this->callsTable->saveCall($call);
    }

    /**
     * Determines if a checklist should run for a specific call
     *
     * @param Call $call
     * @param Company $company
     * @param Checklist $checklist
     * @return bool
     */
    protected function shouldRunChecklist(Call $call, Company $company, Checklist $checklist): bool
    {
        if ($checklist->getCallDurationThreshold() > $call->getDuration()) {
            return false;
        }

        if (!empty($checklist->getCallsStatuses())) {
            if (!in_array($call->getCallStatus(), $checklist->getCallsStatuses(), true)) {
                return false;
            }
        }

        if (!empty($checklist->getCallsTeams())) {
            /** @var Team $agentTeam */
            $agentTeam = $this->teamSelector->getTeams($company->getId(), $call->getAgentId())->first();

            if (!$agentTeam instanceof Team) {
                return false;
            }

            if (!in_array($agentTeam->getId(), $checklist->getCallsTeams(), true)) {
                return false;
            }
        }

        if ($checklist->getCallsScope() !== Checklist::CALLS_SCOPE_ALL) {
            $clientCallsCount = $this->callsTable->getCallsCountByClientId($company->getId(), $call->getClientId());

            if ($clientCallsCount > 1) {
                return false;
            }
        }

        return true;
    }

    private function triggerStepFinishedEvent(string $callId, int $companyId, array $data): void
    {
        $this->eventTrigger->trigger(
            CallAnalysisService::EVENT_CALL_CHECKLIST_STEP_FINISHED,
            [
                'queue_name' => self::CALL_CHECKLIST_QUEUE,
                'company_id' => $companyId,
                'call_id' => $callId,
                'data' => $data
            ]
        );
    }
}
