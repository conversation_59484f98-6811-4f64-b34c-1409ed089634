<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

use Interop\Container\ContainerInterface;
use STCall\Data\CallsParagraphsTable;
use STCall\Data\CallsTable;
use STCall\Entity\CallFactory;
use STCall\Service\CallAnalysis\TranscribingDriver\Provider\DriverProvider;
use STCompany\Data\CompaniesTable;
use STCompany\Service\CompanyVocabularyService;
use STLib\Mvc\Hydrator\Hydrator;
use STRoboTruck\Service\DataCollection\DataCollector;

class TranscribingJobCreationStepFactory implements \Laminas\ServiceManager\Factory\FactoryInterface
{
    /**
     *
     * @param ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return TranscribingJobCreationStep
     */
    public function createService(ContainerInterface $container, $requestedName, array $options = null): TranscribingJobCreationStep
    {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }

    /**
     *
     * @param ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return TranscribingJobCreationStep
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null): TranscribingJobCreationStep
    {
        $callsTable = $container->get(CallsTable::class);
        $callFactory = $container->get(CallFactory::class);
        $companiesTable = $container->get(CompaniesTable::class);
        $hydrator = $container->get(Hydrator::class);
        $driverProvider = $container->get(DriverProvider::class);
        $paragraphTable = $container->get(CallsParagraphsTable::class);
        $companyVocabularyService = $container->get(CompanyVocabularyService::class);
        $dataCollector = $container->get(DataCollector::class);
        $config = $container->get('config');
        return new TranscribingJobCreationStep(
            $callsTable,
            $callFactory,
            $companiesTable,
            $hydrator,
            $driverProvider,
            $paragraphTable,
            $companyVocabularyService,
            $dataCollector,
            $config['aws'],
            $config['wordcab'],
            $config['assembly'],
            $config['transcriptor'],
            $config['speechmatics'],
            $config['salad'],
        );
    }
}
