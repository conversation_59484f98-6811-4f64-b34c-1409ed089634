<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\ResponseConverter;

use ST<PERSON>all\Entity\Paragraph;
use ST<PERSON>all\Entity\ParagraphCollection;
use STLib\Mvc\Hydrator\BaseHydratorTrait;

class Deepgram extends AbstractParagraphResponseConverter
{
    use BaseHydratorTrait;

    /**
     *
     * @inheritdoc
     */
    public function convert(mixed $response): ParagraphCollection
    {
        $paragraphCollection = $this->getParagraphCollectionFromResponse($response);

        if ($paragraphCollection->count() === 0) {
            foreach ($response->results->channels[0]->alternatives[0]->paragraphs->paragraphs as $paragraphNumber => $paragraph) {
                if (!isset($paragraph->speaker)) {
                    continue;
                }

                $sentenses = array_map(function ($sentense) {
                    return $sentense->text;
                }, $paragraph->sentences);
                $start = $paragraph->start;
                $end = $paragraph->end;

                if ($start > $end) {
                    $start = $this->getParagraphSentencesTime($paragraph);
                    $end = $this->getParagraphSentencesTime($paragraph, false);
                }

                $paragraphCollection->add($this->hydrate([
                    'paragraph_number' => $paragraphNumber,
                    'start_time' => $start,
                    'end_time' => $end,
                    'speaker_number' => $paragraph->speaker,
                    'text' => implode(' ', $sentenses),
                ], Paragraph::class));
            }
        }

        return $paragraphCollection;
    }

    /**
     *
     * @param \stdClass $response
     * @return \STCall\Entity\ParagraphCollection
     */
    private function getParagraphCollectionFromResponse(\stdClass $response): \STCall\Entity\ParagraphCollection
    {
        $paragraphCollection = new \STCall\Entity\ParagraphCollection();

        foreach ($response->results->channels[0]->alternatives[0]->paragraphs->paragraphs as $paragraphNumber => $paragraph) {
            if (!isset($paragraph->speaker)) {
                continue;
            }

            $start = isset($paragraph->sentences) ? current($paragraph->sentences)->start : $paragraph->start;
            $end = isset($paragraph->sentences) ? end($paragraph->sentences)->end : $paragraph->end;

            $paragraphCollection->add($this->hydrate([
                'paragraph_number' => $paragraphNumber,
                'start_time' => $start,
                'end_time' => $end,
                'speaker_number' => $paragraph->speaker,
                'text' => implode(' ', array_column($paragraph->sentences, 'text')),
            ], \STCall\Entity\Paragraph::class));
        }

        return $paragraphCollection;
    }

    /**
     *
     * @param \stdClass $paragraph
     * @param bool $start
     * @return float
     */
    private function getParagraphSentencesTime(\stdClass $paragraph, bool $start = true): float
    {
        if ($start) {
            return $paragraph->sentences[0]->start;
        }

        return $paragraph->sentences[\count($paragraph->sentences) - 1]->end;
    }
}
