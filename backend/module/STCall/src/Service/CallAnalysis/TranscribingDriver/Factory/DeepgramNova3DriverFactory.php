<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver\Factory;

use STCall\Service\CallAnalysis\TranscribingDriver\AbstractDriver;
use STCall\Service\CallAnalysis\TranscribingDriver\DeepgramNova3Driver;
use STRoboTruck\Service\DataCollection\DataCollector;

class DeepgramNova3DriverFactory implements FactoryInterface
{
    /**
     * @param DataCollector $dataCollector
     * @return AbstractDriver
     */
    public function create(DataCollector $dataCollector): AbstractDriver
    {
        return new DeepgramNova3Driver($dataCollector);
    }
}
