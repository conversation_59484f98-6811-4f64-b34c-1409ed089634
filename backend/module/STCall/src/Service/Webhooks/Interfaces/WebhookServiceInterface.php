<?php

declare(strict_types=1);

namespace STCall\Service\Webhooks\Interfaces;

interface WebhookServiceInterface
{
    public const string WEBHOOK_TYPE_PARAGRAPHS = 'paragraphs';
    public const string WEBHOOK_TYPE_TRANSLATION = 'translation';
    public const string WEBHOOK_TYPE_EVENTS = 'events';
    public const string WEBHOOK_TYPE_CHECKLISTS = 'checklists';
    public const string WEBHOOK_TYPE_SUMMARIZATION = 'summarization';

    public const array TYPES = [
        self::WEBHOOK_TYPE_PARAGRAPHS,
        self::WEBHOOK_TYPE_TRANSLATION,
        self::WEBHOOK_TYPE_EVENTS,
        self::WEBHOOK_TYPE_CHECKLISTS,
        self::WEBHOOK_TYPE_SUMMARIZATION,
    ];

    public function getType(): string;

    public function filterData(array $data): array;
}
