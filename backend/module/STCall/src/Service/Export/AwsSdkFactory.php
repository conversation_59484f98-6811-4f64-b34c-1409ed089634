<?php

declare(strict_types=1);

namespace STCall\Service\Export;

use Aws\Sdk;
use STCall\Service\Interfaces\ConfigurationInterface;

class AwsSdkFactory
{
    private const string DEFAULT_AWS_S3_REGION = 'eu-central-1';

    public function __construct(private readonly ConfigurationInterface $configuration)
    {
    }

    public function create(string $region = null): Sdk
    {
        $region = $region ?: self::DEFAULT_AWS_S3_REGION;

        $awsConfig = array_merge($this->configuration->get('aws')['api'], ['region' => $region]);

        return new Sdk($awsConfig);
    }
}
