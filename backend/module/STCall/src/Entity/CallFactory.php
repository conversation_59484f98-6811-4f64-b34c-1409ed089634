<?php

declare(strict_types=1);

namespace STCall\Entity;

use RuntimeException;

class CallFactory
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait, \STLib\Mvc\Hydrator\MappingHydratorTrait {
        \STLib\Mvc\Hydrator\BaseHydratorTrait::hydrate insteadof \STLib\Mvc\Hydrator\MappingHydratorTrait;
        \STLib\Mvc\Hydrator\BaseHydratorTrait::extract insteadof \STLib\Mvc\Hydrator\MappingHydratorTrait;
        \STLib\Mvc\Hydrator\BaseHydratorTrait::hydrate as baseHydrate;
        \STLib\Mvc\Hydrator\BaseHydratorTrait::extract as baseExtract;
        \STLib\Mvc\Hydrator\MappingHydratorTrait::hydrate as mapperHydrate;
        \STLib\Mvc\Hydrator\MappingHydratorTrait::extract as mapperExtract;
    }

    /**
     *
     * @param array $params
     * @param bool $withConstructor
     * @return Call
     */
    public function createCall(array $params, bool $withConstructor = false): Call
    {
        if (isset($params['call_type'])) {
            $callType = $params['call_type'];
        } elseif (isset($params['type'])) {
            $callType = $params['type'];
        } else {
            $callType = Call::CALL_TYPE;
        }
        $call = match ($callType) {
            Call::CALL_TYPE => $this->baseHydrate($params, Call::class, $withConstructor),
            ChatCall::CHAT_CALL_TYPE => $this->mapperHydrate($params, ChatCall::class, [
                'call_id' => 'id',
                'call_time' => 'time',
                'call_language' => 'language',
                'call_duration' => 'duration',
                'call_origin' => 'origin',
            ], $withConstructor),
            default => throw new RuntimeException('Unknown call type "' . $callType . '"'),
        };

        // @todo improve this logic
        if (!empty($params['reviewer_user_id'])) {
            $call->addReviewer((int) $params['reviewer_user_id'], $params['reviewer_user_name']);
        }
        return $call;
    }
}
