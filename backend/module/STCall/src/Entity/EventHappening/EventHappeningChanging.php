<?php

declare(strict_types=1);

namespace STCall\Entity\EventHappening;

class EventHappeningChanging
{
    /**
     *
     * @var \STCompany\Entity\Company
     */
    protected \STCompany\Entity\Company $company;

    /**
     *
     * @var string
     */
    protected string $callId;

    /**
     *
     * @var int
     */
    protected int $paragraphNumber;

    /**
     *
     * @var \STCompany\Entity\Event\Event|null
     */
    protected ?\STCompany\Entity\Event\Event $originalEvent;

    /**
     *
     * @var \STCompany\Entity\Event\Event|null
     */
    protected ?\STCompany\Entity\Event\Event $correctedEvent;

    /**
     *
     * @var \STUser\Entity\User
     */
    protected \STUser\Entity\User $user;

    /**
     *
     * @var \STCompany\Entity\Role
     */
    protected \STCompany\Entity\Role $role;

    /**
     *
     * @var string|null
     */
    protected ?string $eventEnHighlight = null;

    /**
     *
     * @var string|null
     */
    protected ?string $eventHighlight = null;

    /**
     *
     * @return \STCompany\Entity\Company
     */
    public function getCompany(): \STCompany\Entity\Company
    {
        return $this->company;
    }

    /**
     *
     * @return string
     */
    public function getCallId(): string
    {
        return $this->callId;
    }

    /**
     *
     * @return int
     */
    public function getParagraphNumber(): int
    {
        return $this->paragraphNumber;
    }

    /**
     *
     * @return \STCompany\Entity\Event\Event|null
     */
    public function getOriginalEvent(): ?\STCompany\Entity\Event\Event
    {
        return $this->originalEvent;
    }

    /**
     *
     * @return \STCompany\Entity\Event\Event|null
     */
    public function getCorrectedEvent(): ?\STCompany\Entity\Event\Event
    {
        return $this->correctedEvent;
    }

    /**
     *
     * @return \STUser\Entity\User
     */
    public function getUser(): \STUser\Entity\User
    {
        return $this->user;
    }

    /**
     *
     * @return \STCompany\Entity\Role
     */
    public function getRole(): \STCompany\Entity\Role
    {
        return $this->role;
    }

    /**
     *
     * @return string|null
     */
    public function getEventEnHighlight(): ?string
    {
        return $this->eventEnHighlight;
    }

    /**
     *
     * @return string|null
     */
    public function getEventHighlight(): ?string
    {
        return $this->eventHighlight;
    }

    /**
     *
     * @param \STCompany\Entity\Company $company
     * @return EventHappeningChanging
     */
    public function setCompany(\STCompany\Entity\Company $company): EventHappeningChanging
    {
        $this->company = $company;
        return $this;
    }

    /**
     *
     * @param string $callId
     * @return EventHappeningChanging
     */
    public function setCallId(string $callId): EventHappeningChanging
    {
        $this->callId = $callId;
        return $this;
    }

    /**
     *
     * @param int $paragraphNumber
     * @return EventHappeningChanging
     */
    public function setParagraphNumber(int $paragraphNumber): EventHappeningChanging
    {
        $this->paragraphNumber = $paragraphNumber;
        return $this;
    }

    /**
     *
     * @param \STCompany\Entity\Event\Event|null $originalEvent
     * @return EventHappeningChanging
     */
    public function setOriginalEvent(?\STCompany\Entity\Event\Event $originalEvent): EventHappeningChanging
    {
        $this->originalEvent = $originalEvent;
        return $this;
    }

    /**
     *
     * @param \STCompany\Entity\Event\Event|null $correctedEvent
     * @return EventHappeningChanging
     */
    public function setCorrectedEvent(?\STCompany\Entity\Event\Event $correctedEvent): EventHappeningChanging
    {
        $this->correctedEvent = $correctedEvent;
        return $this;
    }

    /**
     *
     * @param \STUser\Entity\User $user
     * @return EventHappeningChanging
     */
    public function setUser(\STUser\Entity\User $user): EventHappeningChanging
    {
        $this->user = $user;
        return $this;
    }

    /**
     *
     * @param \STCompany\Entity\Role $role
     * @return EventHappeningChanging
     */
    public function setRole(\STCompany\Entity\Role $role): EventHappeningChanging
    {
        $this->role = $role;
        return $this;
    }

    /**
     *
     * @param string|null $eventEnHighlight
     * @return EventHappeningChanging
     */
    public function setEventEnHighlight(?string $eventEnHighlight): EventHappeningChanging
    {
        $this->eventEnHighlight = $eventEnHighlight;
        return $this;
    }

    /**
     *
     * @param string|null $eventHighlight
     * @return EventHappeningChanging
     */
    public function setEventHighlight(?string $eventHighlight): EventHappeningChanging
    {
        $this->eventHighlight = $eventHighlight;
        return $this;
    }
}
