<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Laminas\Http\Header\ContentType;
use <PERSON>inas\Http\Headers;
use <PERSON>inas\Stdlib\ResponseInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STApi\Entity\Exception\ValidationApiException;
use STLlmEvent\Validator\DeleteLlmEventValidator;
use STLlmEvent\Validator\ImproveLlmEventValidator;
use STLlmEvent\Validator\SaveLlmEventValidator;

class LlmEventController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getEventsAction(): array
    {
        return [
            'events' => $this->llmEvent()->llmEventSelector()->getLlmEvents()->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundApiException
     * @throws NotFoundExceptionInterface
     */
    public function getEventAction(): array
    {
        $id = (int) $this->getApiParam('id');

        return [
            'event' => $this->llmEvent()->llmEventSelector()->getLlmEvent($id)->toArray(),
        ];
    }

    /**
     * @return array|ResponseInterface
     */
    public function improveEventAction(): array|ResponseInterface
    {
        $name = $this->getApiParam('name');
        $description = $this->getApiParam('description');

        $validator = $this->getServiceManager()->get(ImproveLlmEventValidator::class);
        $validator->setInstance(['name' => $name, 'description' => $description]);
        $this->validate($validator);

        try {
            $result = $this->algo()->aiSolutionsCommutator()->improveLlmEvent($name, $description);
        } catch (ThirdPartyApiException $e) {
            $header = new ContentType('application/json');
            $this->getResponse()->getHeaders()->addHeader($header);
            $this->getResponse()->setStatusCode(502);
            $this->getResponse()->setContent(json_encode(['error' => ['messages' => $e->getMessage()]]));

            return $this->getResponse();
        }

        return [
            'event' => $result,
        ];
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     */
    public function saveEventByGlobalAdminAction(): array
    {
        $id = $this->getApiParam('id');
        $name = $this->getApiParam('name');
        $description = $this->getApiParam('description');

        if (!is_null($id)) {
            $id = (int) $id;
        }

        /** @var SaveLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(SaveLlmEventValidator::class);
        $validator->setInstance(['id' => $id, 'name' => $name, 'description' => $description]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $event = $this->llmEvent()->llmEventSaver()->save($name, $description, $id);

        return [
            'event' => $event->toArray(),
        ];
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     * @throws NotFoundApiException
     */
    public function saveEventAction(): array|ResponseInterface
    {
        if (!$this->company->isManageLlmEventsByUsersEnabled()) {
            $error = 'LLM event manage is currently disabled for your company. Contact support for assistance.';
            $this->getResponse()->setStatusCode(403);
            $this->getResponse()
                ->setContent(
                    json_encode([
                        'error' => [
                            'message' => $error
                        ]
                    ])
                );
            $header = new ContentType('application/json');
            $this->getResponse()->getHeaders()->addHeader($header);

            return $this->getResponse();
        }

        $id = $this->getApiParam('id');
        $name = $this->getApiParam('name');
        $description = $this->getApiParam('description');

        if (!is_null($id)) {
            $id = (int) $id;

            $this->company()->llmEventSelector()->getLlmEvent($id, $this->company->getId());

            if ($this->company()->llmEventSelector()->isLlmEventConnectedToAnotherCompany($id, $this->company->getId())) {
                $error = 'This event is also used by other companies and cannot be modified. To make changes, please create your own copy.';
                $this->getResponse()->setStatusCode(422);
                $this->getResponse()
                    ->setContent(
                        json_encode([
                            'error' => [
                                'message' => $error
                            ]
                        ])
                    );
                $header = new ContentType('application/json');
                $this->getResponse()->getHeaders()->addHeader($header);

                return $this->getResponse();
            }
        }

        /** @var SaveLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(SaveLlmEventValidator::class);
        $validator->setInstance(['id' => $id, 'name' => $name, 'description' => $description]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $event = $this->llmEvent()->llmEventSaver()->save($name, $description, $id);

        return [
            'event' => $event->toArray(),
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     * @throws NotFoundExceptionInterface
     */
    public function deleteEventAction(): array
    {
        $id = (int) $this->getApiParam('id');

        /** @var DeleteLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(DeleteLlmEventValidator::class);
        $validator->setInstance($id);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->llmEvent()->llmEventRemover()->delete($id);

        return [
            'message' => 'Event ' . $id . ' has been successfully deleted',
        ];
    }
}
