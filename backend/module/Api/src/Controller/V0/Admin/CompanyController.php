<?php

declare(strict_types=1);

namespace Api\Controller\V0\Admin;

use Api\Controller\V0\BaseController;
use Exception;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use ST<PERSON>pi\Entity\Exception\ValidationApiException;
use STCompany\Validator\CloneRoleValidator;
use STCompany\Validator\UpdateSettingsValidator;

class CompanyController extends BaseController
{
    /**
     * @return array
     * @throws Exception
     */
    public function changeBalanceAction(): array
    {
        $balanceChange = $this->getApiParam('balance');
        $company = $this->company()->getCompany($this->getApiParam('company_id'));

        $company->addPaidTranscribingTime($balanceChange);
        $this->company()->saveCompany($company);

        return [
            'balance' => $company->getPaidTranscribingTime(),
        ];
    }

    /**
     * @return string[]
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     * @throws ReflectionException
     */
    public function cloneRoleAction(): array
    {
        /** @var CloneRoleValidator $validator */
        $validator = $this->getServiceManager()->get(CloneRoleValidator::class);
        $validator->setInstance($this->getApiParams());
        $validator->validate();

        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $sourceCompanyId = (int) $this->getApiParam('source_company_id');
        $sourceRoleId = (int) $this->getApiParam('source_role_id');
        $destinationCompanyId = (int) $this->getApiParam('destination_company_id');
        $destinationRoleId = (int) $this->getApiParam('destination_role_id');

        $sourceRole = $this->company()->role()->getRole($sourceCompanyId, $sourceRoleId);

        if ($destinationRoleId > 0) {
            $destinationRole = $this->company()->role()->getRole($destinationCompanyId, $destinationRoleId);

            $destinationCategories = $this->company()->event()->getCategories(
                $destinationCompanyId,
                $destinationRole->getId()
            );
            $categoryIds = array_column($destinationCategories->toArray(), 'id');

            $this->company()->event()->deleteCategory($destinationRole, $categoryIds);
        } else {
            $destinationRole = $sourceRole;
            $destinationRole
                ->setCompanyId($destinationCompanyId)
                ->setId(null);
            $this->company()->role()->saveRole($destinationRole);
        }

        $this->company()->event()->cloneEventsFromCategories(
            $this->company()->event()->getCategories($sourceCompanyId, $sourceRoleId),
            $destinationRole
        );

        return [
            'status' => 'success'
        ];
    }

    /**
     * @return array
     * @throws ValidationApiException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws Exception
     */
    public function updateSettingsAction(): array
    {
        $companyId = $this->getApiParam('id');
        $this->company()->getCompany($companyId, $this->auth()->getUser()->getId());

        $settingName = $this->getApiParam('setting_name');
        $value = $this->getApiParam('value');

        /** @var UpdateSettingsValidator $validator */
        $validator = $this->getServiceManager()->get(UpdateSettingsValidator::class);
        $validator->setInstance(['setting_name' => $settingName, 'value' => $value]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->company()->updateSettings($companyId, [$settingName => $value]);

        return [
            'company' => $this->company()
                ->getCompany($companyId, $this->auth()->getUser()->getId())
                ->toArray(),
        ];
    }
}
