<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use GuzzleHttp\Exception\GuzzleException;
use Lam<PERSON>\Http\Response;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use ST<PERSON><PERSON>\Entity\Exception\CallUpload\CallUploadedWithoutFileException;
use STApi\Entity\Exception\CallUpload\TooLowBalanceForAnalyzeException;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STApi\Entity\Exception\ValidationApiException;
use STCall\Entity\Call;
use STCall\Entity\ChatCall;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\Precalculation\CallPrecalculationManagerService;
use STCall\Validator\CallFragmentValidator;
use ST<PERSON>lickhouse\Entity\Pagination\Pagination;
use STCompany\Data\EventsColorsTable;
use STCompany\Entity\Client;
use STCompany\Entity\User;

class CallController extends BaseController
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @return array
     */
    public function getCallsAction(): array
    {
        $startDate = $this->hasApiParam('start_date') ? \Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay() : null;
        $endDate = $this->hasApiParam('end_date') ? \Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay() : null;
        $unreadComments = $this->getApiParam('unread_comments') ?? [];

        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());

        $filter = $this->getApiParam('filter') ?? [];
        if ($startDate instanceof \Carbon\Carbon) {
            $filter[] = [
                'column' => 'call_time',
                'value' => $startDate,
                'type' => 'date',
                'compare' => '>=',
            ];
        }
        if ($endDate instanceof \Carbon\Carbon) {
            $filter[] = [
                'column' => 'call_time',
                'value' => $endDate,
                'type' => 'date',
                'compare' => '<=',
            ];
        }
        if (!empty($unreadComments)) {
            $callIds = $this->call()->getCallIdsWithNotifications($this->company->getId(), $user->getId(), $unreadComments);
            $filter['call_id'] = $callIds;
        }

        $pagination = new Pagination();
        $pagination
                ->setFilter($filter)
                ->setResultCallback(function ($record) {
                    $record['fragments'] = array_map(function ($fragment) {
                        $fragment['fragment_number'] = (int) $fragment['fragment_number'];
                        $fragment['start_time'] = (float) $fragment['start_time'];
                        $fragment['end_time'] = (float) $fragment['end_time'];
                        $fragment['active_paragraph_number'] = (int) $fragment['active_paragraph_number'];
                        return $fragment;
                    }, $record['fragments']);
                    $record['events'] = array_map(function ($event) {
                        $event['id'] = (int) $event['id'];
                        $event['paragraph_number'] = (int) $event['paragraph_number'];
                        $event['value'] = (int) $event['value'];
                        return $event;
                    }, $record['events']);
                    $record['comments'] = array_map(function ($comment) {
                        $comment['user_id'] = (int) $comment['user_id'];
                        return $comment;
                    }, $record['comments']);
                    return $record;
                })
                ->getParams()
                ->setPageNumber((int) $this->getApiParam('page_number'))
                ->setItemsOnPage((int) $this->getApiParam('items_on_page'))
                ->setSort($this->getApiParam('sort') ?? [])
                ->addAvailableColumns([
                    'client_name',
                    'agent_name',
                    'call_time',
                    'time',
                    'duration',
                    'call_duration',
                    'language',
                    'call_language',
                    'score',
                    'call_status',
                    'status',
                    'call_type',
                ]);
        $this->call()->getCallsWithPagination($this->company, $user->getRole()->getId(), $pagination);
        return [
            'pagination' => $pagination->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCallAction(): array
    {
        $callId = $this->getApiParam('call_id');
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $eventCollection = $this->company()->event()->getEvents($this->company->getId(), categoryIds: null, roleId: $user->getRole()->getId());
        $defaultFragmentColor = $this->company()->event()->getColor(EventsColorsTable::GREY_COLOR_ID);
        return [
            'call' => $this->call()->getCall($this->company, $user->getRole()->getId(), $user->getId(), $callId, $eventCollection, $defaultFragmentColor)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCallContentAction(): array
    {
        $callId = $this->getApiParam('call_id');
        return [
            'content' => $this->call()->getCallUrl($this->company, $callId),
        ];
    }

    /**
     *
     * @return Response
     */
    public function downloadCallAction(): Response
    {
        /** @var User $user */
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        /** @var Call $call */
        $call = $this->call()->getCall($this->company, $user->getRole()->getId(), $user->getId(), $this->getApiParam('call_id'));
        $startTime = $this->getApiParam('start_time');
        $endTime = $this->getApiParam('end_time');

        /** @var CallFragmentValidator $validator */
        $validator = $this->getServiceManager()->get(CallFragmentValidator::class);
        $validator->setInstance($this->getApiParams());
        $validator->setCallDuration((int) $call->getDuration());
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $callContent = $this->call()->getCallContent($this->company, $call, $startTime, $endTime);

        return $this->output(
            $callContent,
            format: static::MP3_FORMAT,
            options: ['fileName' => 'call_cropped_' . $call->getId()],
        );
    }

    /**
     *
     * @return array
     */
    public function getParagraphAction(): array
    {
        $callId = $this->getApiParam('call_id');
        $paragraphNumber = $this->getApiParam('paragraph_number');
        return [
            'paragraph' => $this->call()->getParagraph($this->company, $callId, $paragraphNumber)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCommentsAction(): array
    {
        $userId = $this->auth()->getUser()->getId();
        $callIds = $this->getApiParam('call_id') ?? [];
        return [
            'comment' => $this->call()->getComments($this->company->getId(), $callIds, $userId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function changeEventHappeningAction(): array
    {
        $originalEventId = (int) $this->getApiParam('original_event_id');
        $correctedEventId = (int) $this->getApiParam('corrected_event_id');
        $callId = $this->getApiParam('call_id');
        $eventEnHighlight = !empty($this->getApiParam('event_en_highlight')) ? $this->getApiParam('event_en_highlight') : null;
        $eventHighlight = !empty($this->getApiParam('event_highlight')) ? $this->getApiParam('event_highlight') : null;

        $role = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId())->getRole();

        $originalEvent = $originalEventId > 0 ? $this->company()->event()->getEvent($this->company->getId(), $originalEventId) : null;
        $correctedEvent = $correctedEventId > 0 ? $this->company()->event()->getEvent($this->company->getId(), $correctedEventId) : null;

        $eventHappeningChanging = $this->hydrate([
            'company' => $this->company,
            'call_id' => $callId,
            'paragraph_number' => (int) $this->getApiParam('paragraph_number'),
            'user' => $this->auth()->getUser(),
            'role' => $role,
            'original_event' => $originalEvent,
            'corrected_event' => $correctedEvent,
            'event_en_highlight' => $eventEnHighlight,
            'event_highlight' => $eventHighlight,
        ], \STCall\Entity\EventHappening\EventHappeningChanging::class);

        $this->call()->eventHappening()->changeEventHappening($eventHappeningChanging);

        return [
            'result' => true,
        ];
    }

    /**
     *
     * @return array
     */
    public function confirmEventHappeningAction(): array
    {
        $callId = $this->getApiParam('call_id');
        $event = $this->company()->event()->getEvent($this->company->getId(), (int) $this->getApiParam('event_id'));

        $role = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId())->getRole();

        $eventHappeningChanging = $this->hydrate([
            'company' => $this->company,
            'original_event' => $event,
            'corrected_event' => $event,
            'call_id' => $callId,
            'paragraph_number' => (int) $this->getApiParam('paragraph_number'),
            'user' => $this->auth()->getUser(),
            'role' => $role,
        ], \STCall\Entity\EventHappening\EventHappeningChanging::class);

        $this->call()->eventHappening()->changeEventHappening($eventHappeningChanging);

        return [
            'result' => true,
        ];
    }


    /**
     *
     * @return array
     * @throws TooLowBalanceForAnalyzeException
     * @throws \STApi\Entity\Exception\CallUpload\EmptyContentException
     */
    public function manualImportAction(): array
    {
        /**
         * naga and manual import. Block them manual import at all
         * @todo Drop it
         */
        if (!$this->company->isManualImportEnabled()) {
            throw new \STApi\Entity\Exception\ValidationApiException('Manual import is blocked for your company');
        }

        $content = file_get_contents($this->getApiParam('file_path'));
        if (empty($content)) {
            throw new \STApi\Entity\Exception\CallUpload\EmptyContentException();
        }
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $agent = $this->hasApiParam('agent_id') ? $this->company()->user()->getUser($this->company->getId(), $this->getApiParam('agent_id')) : null;
        $date = $this->hasApiParam('date') ? \Carbon\Carbon::parse($this->getApiParam('date')) : \Carbon\Carbon::now();

        if (!$this->company->isPaidTranscribingTimeEnoughForAnalyze()) {
            throw new TooLowBalanceForAnalyzeException();
        }

        /** @var \STCall\Service\Import\UploadParams\UploadParams $uploadParams */
        $uploadParams = $this->hydrate(
            [
                'driver_name' => 'manual-upload',
                'content' => $content,
                'company' => $this->company,
                'user' => $user,
                'options' => [
                    'file_name' => $this->getApiParam('file_name'),
                    's3_file_path' => $this->getApiParam('file_name'),
                    'agent' => $agent,
                    'date' => $date,
                ],
            ],
            \STCall\Service\Import\UploadParams\UploadParams::class
        );
        $call = $this->call()->upload()->uploadCall($uploadParams)->getCall();
        $this->call()->analysis()->addToQueue($this->company->getId(), $call, \STCall\Service\CallAnalysis\LanguageDetectionStep::CALL_LANGUAGE_DETECTION_QUEUE);

        $eventCollection = $this->company()->event()->getEvents($this->company->getId(), categoryIds: null, roleId: $user->getRole()->getId());
        $defaultFragmentColor = $this->company()->event()->getColor(EventsColorsTable::GREY_COLOR_ID);

        return [
            'call' => $this->call()->getCall($this->company, $user->getRole()->getId(), $user->getId(), $call->getId(), $eventCollection, $defaultFragmentColor)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function transcribeCallAction(): array
    {
        $callId = $this->getApiParam('call_id');
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $call = $this->call()->getCall($this->company, $user->getRole()->getId(), $user->getId(), $callId);

        if ($this->company->getPaidTranscribingTime() < $call->getDuration()) {
            throw new TooLowBalanceForAnalyzeException();
        }

        if ($call->getCallType() !== ChatCall::CHAT_CALL_TYPE && empty($call->getS3FilePath())) {
            throw new CallUploadedWithoutFileException();
        }

        $call->isSentToTranscribing(true);
        $this->call()->saveCall($call);
        $queue = $this->call()->analysis()->getNextAnalysisStep($call);
        $this->call()->analysis()->addToQueue($this->company->getId(), $call, $queue);

        return [
            'added' => true,
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ReflectionException
     */
    public function reviewCallAction(): array
    {
        $callId = $this->getApiParam('call_id');
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $this->call()->review()->saveReviewed($this->company->getId(), $callId, $user->getRole()->getId(), $user->getId());

        $eventCollection = $this->company()->event()->getEvents($this->company->getId(), categoryIds: null, roleId: $user->getRole()->getId());
        $defaultFragmentColor = $this->company()->event()->getColor(EventsColorsTable::GREY_COLOR_ID);
        $call = $this->call()->getCall($this->company, $user->getRole()->getId(), $user->getId(), $callId, $eventCollection, $defaultFragmentColor);

        $this->call()->precalculationManager()->addCallsToPrecalculateQueue(
            $this->company->getId(),
            $callId,
            CallPrecalculationManagerService::PRECALCULATE_AFTER_REVIEW_CALL_PRIORITY,
            roleId: $user->getRole()->getId(),
        );

        $this->userNotification()->sender()->sendCallEventNotification($call, $user->getName(), $this->front()->getActiveFront());

        return [
            'call' => $call->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function changeLanguageAction(): array
    {
        $callId = $this->getApiParam('call_id');
        $language = $this->getApiParam('language');
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $call = $this->call()->getCall($this->company, $user->getRole()->getId(), $user->getId(), $callId);
        $call
                ->setLanguage($language)
                ->isAnalyzed(false)
                ->isTranscribed(false)
                ->isTranslated(false)
                ->isSpeakersRolesDetected(false)
                ->isChecklistCompleted(false)
                ->isSummarizationCompleted(false)
                ->isLlmEventsDetected(false)
        ;
        $this->call()->saveCall($call);
        return $this->transcribeCallAction();
    }

    /**
     *
     * @return array
     * @throws \STApi\Entity\Exception\ValidationApiException
     */
    public function updateCallAction(): array
    {
        $callId = $this->getApiParam('call_id');
        $clientId = $this->getApiParam('client_id');
        $clientName = $this->getApiParam('client_name') ?? $this->getApiParam('client_id');
        $companyId = $this->company->getId();

        $user = $this->company()->user()->getUser($companyId, $this->auth()->getUser()->getId());
        $eventCollection = $this->company()->event()->getEvents($companyId, categoryIds: null, roleId: $user->getRole()->getId());
        $defaultFragmentColor = $this->company()->event()->getColor(EventsColorsTable::GREY_COLOR_ID);
        /** @var \STCall\Entity\Call $call */
        $call = $this->call()->getCall($this->company, $user->getRole()->getId(), $user->getId(), $callId, $eventCollection, $defaultFragmentColor);

        $call->setClientId($clientId);
        $call->setTime($this->getApiParam('call_time'));
        $call->setAgentId($this->getApiParam('agent_id'));

        $validator = $this->getServiceManager()->get(\STCall\Validator\CallValidator::class);
        $validator->setInstance($call);
        $validator->validate();
        if ($validator->hasError()) {
            throw new \STApi\Entity\Exception\ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $clientService = $this->company()->client();

        try {
            $clientService->getClientById($companyId, $clientId);
        } catch (NotFoundApiException $e) {
            $clientService->saveClient(
                (new Client())
                    ->setCompanyId($companyId)
                    ->setId($clientId)
                    ->setName($clientName)
            );
        }

        $this->call()->saveCall($call);


        // precalculate to sync call_time, agent_id and client_id for raw and precalculated tables
        $this->call()->precalculationManager()->addCallsToPrecalculateQueue(
            $companyId,
            $callId,
            CallPrecalculationManagerService::PRECALCULATE_AFTER_CALL_CHANGE_PRIORITY,
        );

        return [
            'call' => $this->call()->getCall($this->company, $user->getRole()->getId(), $user->getId(), $callId, $eventCollection, $defaultFragmentColor)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function swapSpeakersAction(): array
    {
        $callId = $this->getApiParam('call_id');
        $paragraphs = $this->call()->getParagraphs($this->company, $callId);
        return [
            'paragraphs' => $this->call()->swapCallSpeakerRoles($paragraphs, $this->company)->toArray(),
        ];
    }

    /**
     *
     * @return array
     * @throws \Exception
     */
    public function swapSpeakerAction(): array
    {
        $callId = $this->getApiParam('call_id');
        $paragraphNumber = (int) $this->getApiParam('paragraph_number');

        $paragraph = $this->call()->getParagraph($this->company, $callId, $paragraphNumber);

        return [
            'paragraph' => $this->call()->swapParagraphSpeakerRoles($paragraph, $this->company)->toArray(),
        ];
    }

    /**
     * @return array
     * @throws NotFoundApiException
     * @throws GuzzleException
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     */
    public function updateParagraphAction(): array
    {
        $callId = $this->getApiParam('call_id');
        $paragraphNumber = $this->getApiParam('paragraph_number');
        /** @var \STCall\Entity\Paragraph $paragraph */
        $paragraph = $this->call()->getParagraph($this->company, $callId, $this->getApiParam('paragraph_number'));
        $text = $this->getApiParam('text');
        $enText = $this->getApiParam('en_text');
        $isCallInEnglish = $this->call()->getCallParam($this->company->getId(), $callId, 'call_language') === 'en';

        if (!empty($text)) {
            $paragraph->setText($text);
            $paragraph->setEnText(
                $isCallInEnglish ? $text : $this->translator()->translate($paragraph->getText())
            );
        }

        if (!empty($enText)) {
            $paragraph->setEnText($enText);

            if ($isCallInEnglish) {
                $paragraph->setText($enText);
            }
        }

        $this->call()->saveParagraph($this->company, $paragraph);

        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $eventCollection = $this->company()->event()->getEvents($this->company->getId(), categoryIds: null, roleId: $user->getRole()->getId());
        $defaultFragmentColor = $this->company()->event()->getColor(EventsColorsTable::GREY_COLOR_ID);
        $call = $this->call()->getCall($this->company, $user->getRole()->getId(), $user->getId(), $callId, $eventCollection, $defaultFragmentColor);

        return [
            'paragraph' => $call->getParagraphs()->offsetGet($paragraphNumber)->toArray(),
        ];
    }

    /**
     *
     * @return array
     * @throws \STApi\Entity\Exception\ValidationApiException
     */
    public function saveCommentAction(): array
    {
        $userId = $this->auth()->getUser()->getId();
        $callId = $this->getApiParam('call_id');
        $commentId = $this->getApiParam('comment_id');

        // check if comment belongs to active user
        if (!empty($commentId)) {
            $this->call()->getComment($this->company->getId(), $userId, $commentId);
        }

        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());

        $comment = $this->hydrate([
            'call_id' => $callId,
            'comment_id' => $this->getApiParam('comment_id'),
            'company_id' => $this->company->getId(),
            'user_id' => $this->auth()->getUser()->getId(),
            'message_body' => $this->getApiParam('message_body'),
            'user' => $this->company()->user()->getUser($this->company->getId(), $userId),
        ], \STCall\Entity\Comment::class);

        $mentionedUserIds = $this->company()->user()->filterCompanyUsers($this->company->getId(), $this->getApiParam('mentioned_user_ids'));
        $this->call()->saveComment($comment, $mentionedUserIds);
        $savedComment = $this->call()->getComment($this->company->getId(), $userId, $comment->getCommentId());
        $this->userNotification()->sender()->sendCommentNotification(
            $savedComment,
            $this->front()->getActiveFront(),
            $comment->getNotifiedUserIds(),
        );

        return [
            'comment' => $this->call()->getComments($this->company->getId(), $callId, $userId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     * @throws \STApi\Entity\Exception\ValidationApiException
     */
    public function readCallCommentsAction(): array
    {
        $callId = $this->getApiParam('call_id');
        $userId = $this->auth()->getUser()->getId();
        $this->call()->setCallsCommentsToRead($this->company->getId(), $userId, $callId);

        return [
            'comment' => $this->call()->getComments($this->company->getId(), $callId, $userId)->toArray(),
        ];
    }

    /**
     * @return true[]
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ReflectionException
     */
    public function deleteCallAction(): array
    {
        $callId = $this->getApiParam('call_id');

        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $eventCollection = $this->company()->event()->getEvents($this->company->getId(), categoryIds: null, roleId: $user->getRole()->getId());
        $defaultFragmentColor = $this->company()->event()->getColor(EventsColorsTable::GREY_COLOR_ID);

        $call = $this->call()->getCall($this->company, $user->getRole()->getId(), $user->getId(), $callId, $eventCollection, $defaultFragmentColor);

        $call->setIsDeleted(true);
        $this->call()->saveCall($call);
        $this->call()->checklist()->deleteCallChecklistPoints($call);

        $this->call()->callSummarizationRemover()->removeCallSummarization($call);

        $this->call()->precalculationManager()->addCallsToPrecalculateQueue(
            $this->company->getId(),
            $callId,
            CallPrecalculationManagerService::PRECALCULATE_BULK_PRIORITY,
            roleId: $user->getRole()->getId(),
        );

        return [
            'is_deleted' => true,
        ];
    }

    /**
     * @return string[]
     * @throws NotFoundApiException
     * @throws StepIsAlreadyFinishedException
     * @throws ValidationApiException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function reanalyzeAction(): array
    {
        $callId = $this->getApiParam('call_id');

        $callService = $this->call();
        $analysisService = $this->call()->analysis();

        /** @var Call $call */
        $call = $callService->getBasicCalls($this->company->getId(), [$callId])->current();

        if (!$call) {
            throw new ValidationApiException(json_encode(['There is no call for the provided call_id']));
        }

        if (!$call->isAnalyzed()) {
            $queue = $analysisService->getNextAnalysisStep($call);

            $analysisService->addToQueue($call->getCompanyId(), $call, $queue);

            return [
                'status' => 'scheduled'
            ];
        }

        $call->setLanguage(null)
            ->isTranslated(false)
            ->isTranscribed(false)
            ->isAnalyzed(false)
            ->isSpeakersRolesDetected(false)
            ->isChecklistCompleted(false)
            ->isSummarizationCompleted(false)
            ->isLlmEventsDetected(false)
        ;
        $callService->saveCall($call);
        $callService->deleteCallParagraphs($call);
        $callService->deleteCallAlgoEvents($call);
        $callService->deleteCallEventHappeningChanges($call);
        $this->call()->checklist()->deleteCallChecklistPoints($call);
        $this->call()->callSummarizationRemover()->removeCallSummarization($call);

        $queueName = $analysisService->getNextAnalysisStep($call);
        $analysisService->addToQueue($this->company->getId(), $call, $queueName);

        return [
            'status' => 'scheduled'
        ];
    }
}
