<?php

declare(strict_types=1);

namespace Api\Controller\V0;

class EventCategoryController extends BaseController
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @return array
     */
    public function getEventColorsAction(): array
    {
        return [
            'colors' => $this->company()->event()->getColors()->toArray(),
        ];
    }

    /**
     * @return array
     */
    public function getCategoriesAction(): array
    {
        $roleId = $this->hasApiParam('role_id') ? (int) $this->getApiParam('role_id') : null;
        return [
           'categories' => $this->company()->event()->getCategories($this->company->getId(), $roleId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function getCategoryAction(): array
    {
        $categoryId = (int) $this->getApiParam('category_id');
        return [
            'category' => $this->company()->event()->getCategory($this->company->getId(), $categoryId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     * @throws \STApi\Entity\Exception\ValidationApiException
     */
    public function saveCategoryAction(): array
    {
        $categoryId = (int) $this->getApiParam('category_id');
        $category = $this->hydrate($this->getApiParams()->toArray(), \STCompany\Entity\Event\Category::class, withConstructor: true);
        $roleId = $this->getApiParam('role_id');
        $role = $this->company()->role()->getRole($this->company->getId(), $roleId);
        $category->setRole($role);
        $category->getColor()->setId($this->getApiParam('color_id'));

        if ($categoryId > 0) {
            // call to check access to category
            $this->company()->event()->getCategory($this->company->getId(), $categoryId);
            $category->setId($categoryId);
        }

        $validator = $this->getServiceManager()->get(\STCompany\Validator\EventCategoryValidator::class);
        $validator->setInstance($category);
        $validator->validate();
        if ($validator->hasError()) {
            throw new \STApi\Entity\Exception\ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $savedCategoryId = $this->company()->event()->saveCategory($category);

        return [
            'category' => $this->company()->event()->getCategory($this->company->getId(), $savedCategoryId)->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function deleteCategoryAction(): array
    {
        $categoryId = (int) $this->getApiParam('category_id');
        // call to check access to category
        $category = $this->company()->event()->getCategory($this->company->getId(), $categoryId);
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        $this->company()->event()->deleteCategory($user->getRole(), $category->getId());
        return [
            'deleted' => true,
        ];
    }
}
