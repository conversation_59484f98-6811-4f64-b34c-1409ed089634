<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Exception;
use STCompany\Entity\Role;

class UserController extends BaseController
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     *
     * @return array
     */
    public function getUserAction(): array
    {
        return [
            'user' => $this->auth()->getUser()->toArray(),
        ];
    }

    /**
     *
     * @return array
     * @throws \STApi\Entity\Exception\ValidationApiException
     */
    public function changePasswordAction(): array
    {
        $config = $this->getServiceManager()->get('config');

        $validator = $this->getServiceManager()->get(\STUser\Validator\UserValidator::class);
        $validator->setInstance($this->hydrate($this->getApiParams()->toArray(), \STUser\Entity\User::class));
        $validator->validate('password', 'password-confirm');
        if ($validator->hasError()) {
            throw new \STApi\Entity\Exception\ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        /** @var \STUser\Entity\User $user */
        $user = $this->auth()->getUser();
        $user->setPassword($this->getApiParam('password'));
        $user->hashPassword($config['auth']['salt']);
        $user->isFirstLogin(false);
        $this->user()->saveUser($user, updatePassword: true);

        return [
            'user' => $this->auth()->getUser()->toArray(),
        ];
    }

    /**
     *
     * @return array
     * @throws Exception
     */
    public function getUserCompaniesAction(): array
    {
        $userId = $this->auth()->getUser()->getId();
        $companies = $this->company()->getCompaniesByUserId($userId);
        $companyIds = array_column($companies->toArray(), 'id');
        $roles = $this->company()->role()->getRoles($companyIds, [
            Role::COMPANY_ADMIN_ROLE_TYPE,
            Role::COMPANY_SUPERVISOR_ROLE_TYPE,
            Role::MANAGER_ROLE_TYPE,
            Role::AGENT_ROLE_TYPE,
        ]);
        return [
            'companies' => $companies->toArray(),
            'roles' => $roles->toArray([
                'id',
                'company_id',
                'name',
            ]),
        ];
    }

    /**
     *
     * @return array
     */
    public function getUserPermissionsAction(): array
    {
        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());
        return [
            'permissions' =>  $user->getRole()->getPermissions()->toArray(),
        ];
    }

    /**
     *
     * @return array
     */
    public function saveUserAction(): array
    {
        $user = $this->auth()->getUser();

        $validator = $this->getServiceManager()->get(\STUser\Validator\UserValidator::class);
        $validator->setInstance($this->hydrate($this->getApiParams()->toArray(), \STUser\Entity\User::class));
        $validator->validate('name', 'avatar');

        if ($validator->hasError()) {
            throw new \STApi\Entity\Exception\ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $user->setName($this->getApiParam('name'));
        $user->setAvatar($this->getApiParam('avatar'));
        $this->user()->saveUser($user);

        $this->user()->avatar()->deleteAvatar($user);

        return [
            'user' => $this->auth()->getUser()->toArray(),
        ];
    }
}
