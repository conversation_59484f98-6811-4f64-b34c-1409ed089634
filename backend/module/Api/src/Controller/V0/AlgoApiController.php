<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use GuzzleHttp\Exception\GuzzleException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use ST<PERSON>lgo\Validator\CompareAlgoApisValidator;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ThirdPartyApiException;
use STApi\Entity\Exception\ValidationApiException;
use STCall\Entity\Call;

class AlgoApiController extends \Api\Controller\V0\BaseController
{
    /**
     * @return array
     * @throws GuzzleException
     * @throws NotFoundApiException
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     * @throws ValidationApiException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function compareAlgoApisAction(): array
    {
        /** @var Call $call */
        $call = $this->call()->getBasicCalls($this->company->getId(), [$this->getApiParam('call_id')])->first();

        if (empty($call)) {
            throw new NotFoundApiException('Call not found');
        }

        /** @var CompareAlgoApisValidator $validator */
        $validator = $this->getServiceManager()->get(CompareAlgoApisValidator::class);
        $validator->setInstance($this->getApiParam('algo_apis'));
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $algoApiRequests = $this->algo()->eventsAlgoApiRequestCreator()->create(
            $this->company,
            $call,
            $this->getApiParam('algo_apis'),
            withoutLlm: true
        );

        return $this->algo()->aiSolutionsCommutator()->compareAlgoApis(
            $this->company,
            $call,
            $algoApiRequests
        );
    }

    /**
     *
     * @return array
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function getAlgoApisAction(): array
    {
        return [
            'algo_apis' => $this->algo()->api()->getAlgoApis()->toArray()
        ];
    }
}
