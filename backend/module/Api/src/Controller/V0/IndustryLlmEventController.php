<?php

namespace Api\Controller\V0;

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ValidationApiException;
use STInd<PERSON>ry\Validator\IndustryExistsValidator;
use STIndustry\Validator\LlmEvent\ConnectLlmEventValidator;
use STIndustry\Validator\LlmEvent\DisconnectLlmEventValidator;

class IndustryLlmEventController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function getEventsAction(): array
    {
        $industryId = (int) $this->getApiParam('id');

        /** @var IndustryExistsValidator $validator */
        $validator = $this->getServiceManager()->get(IndustryExistsValidator::class);
        $validator->setInstance($industryId);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        return [
            'events' => $this->industry()->llmEventSelector()->getLlmEvents($industryId)->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundApiException
     * @throws NotFoundExceptionInterface
     */
    public function getEventAction(): array
    {
        $industryId = (int) $this->getApiParam('id');
        $llmEventId = (int) $this->getApiParam('llm_event_id');

        return [
            'event' => $this->industry()->llmEventSelector()->getLlmEvent($llmEventId, $industryId)->toArray(),
        ];
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws NotFoundApiException
     * @throws ContainerExceptionInterface
     * @throws ValidationApiException
     */
    public function connectEventAction(): array
    {
        $industryId = (int) $this->getApiParam('id');
        $llmEventId = (int) $this->getApiParam('llm_event_id');

        /** @var ConnectLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(ConnectLlmEventValidator::class);
        $validator->setInstance(['llm_event_id' => $llmEventId, 'industry_id' => $industryId]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $event = $this->industry()->llmEventConnector()->connect($llmEventId, $industryId);

        return [
            'event' => $event->toArray(),
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function disconnectEventAction(): array
    {
        $industryId = (int) $this->getApiParam('id');
        $llmEventId = (int) $this->getApiParam('llm_event_id');

        /** @var DisconnectLlmEventValidator $validator */
        $validator = $this->getServiceManager()->get(DisconnectLlmEventValidator::class);
        $validator->setInstance(['llm_event_id' => $llmEventId, 'industry_id' => $industryId]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->industry()->llmEventConnector()->disconnect($llmEventId, $industryId);

        return [
            'is_deleted' => true,
            'message' => 'Successfully disconnected event from industry.',
        ];
    }
}
