<?php

declare(strict_types=1);

namespace Console\Command\Call;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class AnalysisStepCommand extends \Console\Command\BaseCommand
{
    /**
     *
     * @var string
     */
    protected static $defaultName = 'call:analysis:daemon';

    /**
     *
     * @var string
     */
    protected static $defaultDescription = 'Collect analysis step';

    /**
     *
     * @return void
     */
    protected function configure()
    {
        parent::configure();
        $this->addOption('step', null, \Symfony\Component\Console\Input\InputOption::VALUE_REQUIRED, 'step');
    }

    /**
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        $stepName = $input->getOption('step');
        $step = $this->call()->analysisStep($stepName);
        $daemon = $this->getDaemon($stepName);
        $daemon
                ->setQueueName($step->getQueueName())
                ->setErrorQueueName($step->getErrorQueueName());
        $daemon->params()->add($step, 'step');
        $daemon->params()->add($this->call()->analysis(), \STCall\Service\CallAnalysisService::class);
        $daemon->params()->add($this->getServiceManager()->get(\Laminas\Db\Adapter\Adapter::class), \Laminas\Db\Adapter\Adapter::class);
        $this->rabbit()->daemon()->run($daemon);
        return static::FAILURE;
    }

    /**
     *
     * @param string $step
     * @return \STCall\Daemon\Analysis\BaseAnalysisStepDaemon
     * @throws \LogicException
     */
    protected function getDaemon(string $step): \STCall\Daemon\Analysis\BaseAnalysisStepDaemon
    {
        $dashToCamelCase = new \Laminas\Filter\Word\DashToCamelCase();
        $namespace = '\STCall\Daemon\\Analysis\\' . $dashToCamelCase->filter($step) . 'StepDaemon';
        if (!class_exists($namespace)) {
            throw new \LogicException('Can\'t find daemon for "' . $namespace . '"');
        }
        return $this->getServiceManager()->get($namespace);
    }
}
