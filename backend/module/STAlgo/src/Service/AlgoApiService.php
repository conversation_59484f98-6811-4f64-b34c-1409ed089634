<?php

declare(strict_types=1);

namespace STAlgo\Service;

use ReflectionException;
use STAlgo\Data\AlgoApisTable;
use STAlgo\Data\CompaniesAlgoApisTable;
use STAlgo\Entity\AlgoApi;
use STAlgo\Entity\AlgoApiCollection;
use STApi\Entity\Exception\NotFoundApiException;
use STLib\Db\ProvidesTransaction;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use STLib\Mvc\Hydrator\Hydrator;

class AlgoApiService
{
    use ProvidesTransaction;
    use BaseHydratorTrait;

    /**
     *
     * @var AlgoApisTable
     */
    protected AlgoApisTable $algoApisTable;

    /**
     *
     * @var CompaniesAlgoApisTable
     */
    protected CompaniesAlgoApisTable $companiesAlgoApisTable;

    /**
     * @param AlgoApisTable $algoApisTable
     * @param CompaniesAlgoApisTable $companiesAlgoApisTable
     * @param Hydrator $hydrator
     */
    public function __construct(
        AlgoApisTable $algoApisTable,
        CompaniesAlgoApisTable $companiesAlgoApisTable,
        private readonly Hydrator $hydrator
    ) {
        $this->algoApisTable = $algoApisTable;
        $this->companiesAlgoApisTable = $companiesAlgoApisTable;
    }

    /**
     * @param array|null $algoApiIds
     * @return AlgoApiCollection
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function getAlgoApis(?array $algoApiIds = null): AlgoApiCollection
    {
        if (!is_null($algoApiIds)) {
            $algoApisResultSet = $this->algoApisTable->getApis($algoApiIds);
        } else {
            $algoApisResultSet = $this->algoApisTable->getApis();
        }

        $algoApiCollection = new AlgoApiCollection();
        foreach ($algoApisResultSet->toArray() as $algoApiData) {
            /**
             * @var AlgoApi $algoApi
             */
            $algoApi = $this->hydrator->hydrateClass($algoApiData, AlgoApi::class);
            $algoApiCollection->add($algoApi);
        }

        return $algoApiCollection;
    }

    /**
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function getNerAlgoApisIndexedByPath(): AlgoApiCollection
    {
        $indexedByPathAlgoApiCollection = new AlgoApiCollection();

        foreach ($this->getAlgoApis() as $algoApi) {
            if ($algoApi->getAnalyzeMethod() !== CompaniesAlgoApisTable::NER_ANALYZE_METHOD) {
                continue;
            }
            $indexedByPathAlgoApiCollection->add($algoApi, $algoApi->getPath());
        }

        return $indexedByPathAlgoApiCollection;
    }

    /**
     *
     * @param int $algoApiId
     * @return AlgoApi
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function getAlgoApi(int $algoApiId): AlgoApi
    {
        $algoApiData = $this->algoApisTable->getApis($algoApiId);
        /** @var AlgoApi $algoApi */
        $algoApi = $this->hydrate($algoApiData->current()->getArrayCopy(), AlgoApi::class);
        return $algoApi;
    }

    /**
     *
     * @return array
     */
    public function getDefaultApiIds(): array
    {
        return array_column($this->algoApisTable->getDefaultApiIds()->toArray(), 'algo_api_id');
    }

    /**
     *
     * @param int $companyId
     * @return array
     */
    public function getCompanyAlgoApiIds(int $companyId): array
    {
        return array_column($this->companiesAlgoApisTable->getCompanyAlgoApiIds($companyId)->toArray(), 'algo_api_id');
    }

    /**
     *
     * @param AlgoApi $algoApi
     * @return int
     */
    public function saveAlgoApi(AlgoApi $algoApi): int
    {
        return $this->algoApisTable->saveAlgoApi($algoApi);
    }

    /**
     *
     * @param int $companyId
     * @param array $algoApiIds
     * @return int
     * @throws \Exception
     */
    public function saveCompanyAlgoApiIds(int $companyId, array $algoApiIds): int
    {
        $this->beginTransaction();
        try {
            $this->companiesAlgoApisTable->deleteCompaniesAlgoApis($companyId);
            $this->companiesAlgoApisTable->saveCompanyAlgoApiIds($companyId, $algoApiIds);
            $this->commit();
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
        return count($algoApiIds);
    }
}
