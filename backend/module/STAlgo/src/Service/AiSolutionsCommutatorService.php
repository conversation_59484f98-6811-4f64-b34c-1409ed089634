<?php

declare(strict_types=1);

namespace STAlgo\Service;

use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use ReflectionException;
use RuntimeException;
use STAlgo\DTO\AlgoApiChecklistResponse;
use STAlgo\DTO\AlgoApiSpeakersRolesResponseDTO;
use STAlgo\Service\AlgoEvents\RequestCreation\EventsAlgoApiRequestInterface;
use STAlgo\Service\AlgoEvents\RequestCreation\NerEventsAlgoApiRequest;
use STAlgo\Service\AlgoEvents\RequestCreation\ResponseToAlgoEventConverter;
use STAlgo\Service\Interfaces\TranslatorInterface;
use STApi\Entity\Exception\ThirdPartyApiException;
use STCall\Entity\AlgoEventCollection;
use STCall\Entity\Call;
use STCall\Entity\Paragraph;
use ST<PERSON>all\Entity\ParagraphCollection;
use STCall\Helper\LanguageHelper;
use STCompany\Entity\Checklist\ChecklistPoint;
use STCompany\Entity\Checklist\ChecklistPointCollection;
use STCompany\Entity\Company;
use STCompany\Entity\CompanyDetails;
use STLib\Db\ProvidesTransaction;
use STLib\Mvc\Hydrator\BaseHydratorTrait;
use Throwable;

class AiSolutionsCommutatorService
{
    use ProvidesTransaction;
    use BaseHydratorTrait;

    private const string CHECKLIST_ENDPOINT = 'checklistV5';
    /**
     *
     * @var Client
     */
    protected Client $algoClient;

    /**
     * @param Client $algoClient
     * @param TranslatorInterface $translator
     * @param ResponseToAlgoEventConverter $responseToAlgoEventConverter
     */
    public function __construct(
        Client $algoClient,
        private readonly TranslatorInterface $translator,
        private readonly ResponseToAlgoEventConverter $responseToAlgoEventConverter,
    ) {
        $this->algoClient = $algoClient;
    }

    /**
     * @param Company $company
     * @param Call $call
     * @param NerEventsAlgoApiRequest[] $algoApiRequests
     * @return array|array[]
     * @throws GuzzleException
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     */
    public function compareAlgoApis(
        Company $company,
        Call $call,
        array $algoApiRequests,
    ): array {
        if (empty($algoApiRequests)) {
            throw new RuntimeException('No algo apis to compare');
        }

        $algoApisComparisonResult = [
            'call' => [
                'call_id' => $call->getId(),
                'paragraphs' => current($algoApiRequests)->getParams()['paragraphs'],
            ]
        ];

        foreach ($algoApiRequests as $algoApiRequest) {
            try {
                $algoEventCollection = $this->getAlgoEventsFromCall(
                    $company,
                    $call,
                    [$algoApiRequest]
                );
            } catch (ClientException $e) {
                continue;
            }

            foreach ($algoEventCollection as $algoEvent) {
                $algoEvent->setEnMainPointPhrase(
                    $this->translator->translate(
                        $algoEvent->getMainPointPhrase(),
                    )
                );
            }

            $algoApisComparisonResult['algo_apis'][] = [
                'algo_api_id' => $algoApiRequest->getAlgoApiId(),
                'algo_api_path' => $algoApiRequest->getAlgoApiPath(),
                'env' => $algoApiRequest->getEnv(),
                'algo_events' => $algoEventCollection->toArray()
            ];
        }

        return $algoApisComparisonResult;
    }

    /**
     * @param Company $company
     * @param Call $call
     * @param EventsAlgoApiRequestInterface[] $algoApiRequests
     * @return AlgoEventCollection
     * @throws GuzzleException
     * @throws ReflectionException
     */
    public function getAlgoEventsFromCall(
        Company $company,
        Call $call,
        array $algoApiRequests,
    ): AlgoEventCollection {
        $algoEventCollection = new AlgoEventCollection();

        foreach ($algoApiRequests as $algoApiRequest) {
            $this->callAlgoApi(
                $algoEventCollection,
                $company,
                $call,
                $algoApiRequest
            );
        }

        return $algoEventCollection;
    }

    /**
     *
     * @param Call $call
     * @param ParagraphCollection $paragraphCollection
     * @return AlgoApiSpeakersRolesResponseDTO
     * @throws GuzzleException
     * @throws JsonException
     */
    public function getParagraphsSpeakersRoles(
        Call $call,
        ParagraphCollection $paragraphCollection
    ): AlgoApiSpeakersRolesResponseDTO {
        $paragraphs = [];

        /** @var Paragraph $paragraph */
        foreach ($paragraphCollection as $paragraph) {
            $paragraphs[] = [
                'id' => $paragraph->getParagraphNumber(),
                'text' => $paragraph->getText(),
                'en_text' => $paragraph->getEnText() ?? $paragraph->getText(),
                'speaker_number' => $paragraph->getSpeakerNumber(),
            ];
        }

        $requestParams = [
            'paragraphs' => $paragraphs,
            'company_id' => (string) $call->getCompanyId(),
            'call_id' => $call->getId(),
        ];

        if ($call->getLanguage() !== null && LanguageHelper::hasLanguageCodeByLanguage($call->getLanguage())) {
            $requestParams['language'] = LanguageHelper::getLanguageCodeByLanguage($call->getLanguage());
        }

        $speakersResult = json_decode(
            $this->algoClient->getSpeakers($requestParams),
            true,
            512,
            JSON_THROW_ON_ERROR
        );

        return new AlgoApiSpeakersRolesResponseDTO(
            status: $speakersResult['status'],
            results: $speakersResult['results']
        );
    }

    /**
     * @param Call $call
     * @param ParagraphCollection $paragraphCollection
     * @param ChecklistPointCollection $checklistPoints
     * @param CompanyDetails|null $companyDetails
     * @return AlgoApiChecklistResponse
     * @throws GuzzleException
     * @throws JsonException
     */
    public function getChecklistResult(
        Call $call,
        ParagraphCollection $paragraphCollection,
        ChecklistPointCollection $checklistPoints,
        ?CompanyDetails $companyDetails = null,
    ): AlgoApiChecklistResponse {
        $paragraphs = [];

        /** @var Paragraph $paragraph */
        foreach ($paragraphCollection as $paragraph) {
            $paragraphs[] = [
                'id' => $paragraph->getParagraphNumber(),
                'text' => $paragraph->getText(),
                'en_text' => $paragraph->getEnText() ?? $paragraph->getText(),
                'speaker_number' => $paragraph->getSpeakerNumber(),
            ];
        }

        $requestParams = [
            'events' => $this->getChecklistEventsFromChecklistPoints($checklistPoints),
            'paragraphs' => $paragraphs,
            'company_id' => (string) $call->getCompanyId(),
            'call_id' => $call->getId(),
        ];

        if ($companyDetails !== null && $companyDetails->getId() !== null) {
            $requestParams['company_details'] = $companyDetails->toArray();
        }

        if ($call->getLanguage() !== null && LanguageHelper::hasLanguageCodeByLanguage($call->getLanguage())) {
            $requestParams['language'] = LanguageHelper::getLanguageCodeByLanguage($call->getLanguage());
        }

        $checklistResult = $this->algoClient->getChecklistResult($requestParams);

        return new AlgoApiChecklistResponse(
            status: $checklistResult['status'],
            results: $checklistResult['results']
        );
    }

    /**
     * @throws ThirdPartyApiException
     */
    public function improveLlmEvent(string $name, string $description): array
    {
        try {
            $data = $this->algoClient->improveLlmEvent($name, $description);
        } catch (Throwable $e) {
            throw new ThirdPartyApiException('Failed to improve LLM event.');
        }

        if (
            !array_key_exists('status', $data) ||
            $data['status'] !== 'ok' ||
            !array_key_exists('results', $data) ||
            !array_key_exists('title', $data['results']) ||
            !array_key_exists('description', $data['results'])
        ) {
            throw new ThirdPartyApiException('Failed to improve LLM event.');
        }

        return [
            'name' => $data['results']['title'],
            'description' => $data['results']['description'],
        ];
    }

    /**
     * @throws ThirdPartyApiException
     */
    public function improveChecklistPoint(
        string $title,
        string $expectedActions,
        string $goodPerformanceDescription,
        string $badPerformanceDescription
    ): array {
        $params = [
            'endpoint' => self::CHECKLIST_ENDPOINT,
            'event' => [
                'title' => $title,
                'expected_criteria' => $expectedActions,
                'green_assessment_description' => $goodPerformanceDescription,
                'red_assessment_description' => $badPerformanceDescription,
            ]
        ];

        try {
            $data = $this->algoClient->improveChecklistPoint($params);
        } catch (Throwable $e) {
            throw new ThirdPartyApiException('Failed to improve checklist point.');
        }

        if (
            !array_key_exists('status', $data) ||
            $data['status'] !== 'ok' ||
            !array_key_exists('results', $data) ||
            !array_key_exists('expected_criteria', $data['results']) ||
            !array_key_exists('green_assessment_description', $data['results']) ||
            !array_key_exists('red_assessment_description', $data['results'])
        ) {
            throw new ThirdPartyApiException('Failed to improve checklist point.');
        }

        return [
            'title' => $data['results']['title'],
            'expected_actions' => $data['results']['expected_criteria'],
            'good_performance_description' => $data['results']['green_assessment_description'],
            'bad_performance_description' => $data['results']['red_assessment_description'],
        ];
    }

    /**
     * @param AlgoEventCollection $algoEventCollection
     * @param Company $company
     * @param Call $call
     * @param EventsAlgoApiRequestInterface $algoApiRequest
     * @throws GuzzleException
     * @throws ReflectionException
     */
    private function callAlgoApi(
        AlgoEventCollection $algoEventCollection,
        Company $company,
        Call $call,
        EventsAlgoApiRequestInterface $algoApiRequest,
    ): void {
        $jsonResponse = $this->algoClient->analyze($algoApiRequest);

        $this->responseToAlgoEventConverter->convert(
            $algoEventCollection,
            $jsonResponse,
            $company,
            $call,
            $algoApiRequest
        );
    }

    protected function getChecklistEventsFromChecklistPoints(ChecklistPointCollection $checklistPoints): array
    {
        $events = [];

        /**
         * @var ChecklistPoint $checklistPoint
         */
        foreach ($checklistPoints as $checklistPoint) {
            $event = [
                'title' => $checklistPoint->getTitle(),
                'expected_criteria' => $checklistPoint->getExpectedActions(),
                'job_description' => '',
                'condition' => $checklistPoint->isOptional(),
                'green_assessment_description' => $checklistPoint->getGoodPerformanceDescription(),
                'green_assessment_example' => (string) $checklistPoint->getGoodPerformanceExample(),
                'red_assessment_description' => $checklistPoint->getBadPerformanceDescription(),
                'red_assessment_example' => (string) $checklistPoint->getBadPerformanceExample(),
                'trigger_condition' => (string) $checklistPoint->getTriggerCondition(),
            ];

            $events[] = $event;
        }

        return $events;
    }
}
