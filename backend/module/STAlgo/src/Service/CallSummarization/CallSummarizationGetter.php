<?php

declare(strict_types=1);

namespace STAlgo\Service\CallSummarization;

use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use ReflectionException;
use STAlgo\Service\Client;
use STAlgo\Service\ParamsBuilding\RequestParamsBuilder;
use ST<PERSON>pi\Entity\Exception\ThirdPartyApiException;
use ST<PERSON>all\Entity\Call;
use STCall\Entity\CallSummarization;
use STCall\Service\Interfaces\CompanySelectorInterface;
use STCompany\Entity\Company;

final readonly class CallSummarizationGetter
{
    public function __construct(
        private RequestParamsBuilder $requestParamsBuilder,
        private CompanySelectorInterface $companySelector,
        private Client $client
    ) {
    }

    /**
     * @param Company $company
     * @param Call $call
     * @return CallSummarization
     * @throws GuzzleException
     * @throws JsonException
     * @throws ReflectionException
     * @throws ThirdPartyApiException
     */
    public function getSummarization(Company $company, Call $call): CallSummarization
    {
        $requestParams = $this->requestParamsBuilder->build($company, $call);
        $companyDetails = $this->companySelector->getCompanyDetails($company->getId());

        if ($companyDetails->getId() !== null) {
            $requestParams->addParams('company_details', $companyDetails->toArray());
        }

        $callSummarizationData = $this->client->getCallSummarization($requestParams);

        if (!$this->isDataValid($callSummarizationData)) {
            throw new ThirdPartyApiException('Unexpected response from algo: ' . json_encode($callSummarizationData));
        }

        $callSummarization = new CallSummarization();
        $callSummarization->setCallId($call->getId());
        $callSummarization->setCompanyId($company->getId());
        $callSummarization->setKeyPoints($callSummarizationData['results']['Key Points']);
        $callSummarization->setCustomerSentiment($callSummarizationData['results']['Customer Sentiment']);
        $callSummarization->setNextSteps($callSummarizationData['results']['Next Steps']);
        $callSummarization->setPrimaryPurpose($callSummarizationData['results']['Primary Purpose']);
        $callSummarization->setMainTopics($callSummarizationData['results']['Main Topics']);
        $callSummarization->setCustomerProblems($callSummarizationData['results']['Customer\'s Problems']);
        $callSummarization->setKeyActionItems($callSummarizationData['results']['Key Action Items']);
        $callSummarization->setBusinessOpportunities($callSummarizationData['results']['Business Opportunities']);
        $callSummarization->setRisks($callSummarizationData['results']['Risks']);
        $callSummarization->setConversationType($callSummarizationData['results']['Conversation Type']);
        $callSummarization->setOverview($this->formatOverview($callSummarizationData));
        $callSummarization->setDetails($this->formatDetails($callSummarizationData));

        return $callSummarization;
    }

    private function isDataValid(array $data): bool
    {
        if (!array_key_exists('results', $data)) {
            return false;
        }

        $requiredFields = [
            'Key Points',
            'Customer Sentiment',
            'Next Steps',
            'Primary Purpose',
            'Main Topics',
            'Customer\'s Problems',
            'Key Action Items',
            'Business Opportunities',
        ];

        foreach ($requiredFields as $field) {
            if (!array_key_exists($field, $data['results'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Formats the overview section of the call summarization
     *
     * @param array $data The call summarization data
     * @return string The formatted overview
     */
    private function formatOverview(array $data): string
    {
        return <<<EOT
        # Overview
        ## Key Points
        {$data['results']['Key Points']}
        
        ## Customer Sentiment
        {$data['results']['Customer Sentiment']}
        
        ## Next Steps
        {$data['results']['Next Steps']}
        EOT;
    }

    /**
     * Formats the details section of the call summarization
     *
     * @param array $data The call summarization data
     * @return string The formatted details
     */
    private function formatDetails(array $data): string
    {
        return <<<EOT
        # Details
        ## Overview
        ### Primary Purpose
        {$data['results']['Primary Purpose']}
        
        ## Key Discussion Points
        ### Main Topics
        {$data['results']['Main Topics']}
        
        ### Customer's Problems
        {$data['results']['Customer\'s Problems']}
        
        ### Key Action Items
        {$data['results']['Key Action Items']}
        
        ### Business Opportunities
        {$data['results']['Business Opportunities']}
        EOT;
    }
}
