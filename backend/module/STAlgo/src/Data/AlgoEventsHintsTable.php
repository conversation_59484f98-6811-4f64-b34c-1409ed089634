<?php

declare(strict_types=1);

namespace STAlgo\Data;

class AlgoEventsHintsTable extends \STLib\Db\AbstractTable
{
    /**
     *
     * @param int $algoApiId
     * @param string $algoEvent
     * @param string $algoEventHint
     * @return bool
     */
    public function updateAlgoEventHint(int $algoApiId, string $algoEvent, string $algoEventHint): bool
    {
        return $this->insertOrUpdate([
            'algo_api_id' => $algoApiId,
            'algo_event' => $algoEvent,
            'algo_event_hint' => $algoEventHint,
        ], [
            'algo_event_hint' => $algoEventHint,
        ]);
    }
}
