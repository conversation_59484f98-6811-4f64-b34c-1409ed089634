<?php

declare(strict_types=1);

namespace tests\Feature\Admin\LlmEvents;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Entity\Role;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;
use tests\Feature\AuthTestCase;

final class UpdateLlmEventTest extends AuthTestCase
{
    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testUpdate(): void
    {
        $llmEventId = $this->faker->numberBetween(1, 100);

        $name = 'Event name';
        $description = 'Event description';

        $existentLlmEvent = $this->createMock(LlmEvent::class);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable->method('getLlmEvent')->with($llmEventId)->willReturn($existentLlmEvent);
        $llmEventsTable
            ->expects($this->once())
            ->method('saveLlmEvent')
            ->with(
                self::callback(
                    function (LlmEvent $llmEvent) use ($llmEventId, $name, $description): bool {
                        return $llmEvent->getId() === $llmEventId
                            && $name === $llmEvent->getName()
                            && $description === $llmEvent->getDescription();
                    }
                )
            );
        $this->serviceManager->setService(LlmEventsTable::class, $llmEventsTable);

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->getRequest()->setContent(json_encode([
            'name' => $name,
            'description' => $description,
        ]));
        $this->dispatchApi('admin/llm-event/' . $llmEventId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $expectedData = [
            'event' => [
                'id' => $llmEventId,
                'name' => $name,
                'description' => $description,
            ]
        ];

        $this->assertSame($expectedData, $response['result']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenNonGlobalAdmin(): void
    {
        $llmEventId = $this->faker->numberBetween(1, 100);

        $this->loginAs(Role::COMPANY_ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/llm-event/' . $llmEventId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'Method is available for global admins only',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(401);
    }
}
