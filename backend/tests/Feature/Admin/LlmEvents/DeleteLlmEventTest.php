<?php

declare(strict_types=1);

namespace tests\Feature\Admin\LlmEvents;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Entity\Role;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;
use tests\Feature\AuthTestCase;

final class DeleteLlmEventTest extends AuthTestCase
{
    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testDelete(): void
    {
        $llmEventId = $this->faker->numberBetween(1, 100);

        $existentLlmEvent = $this->createMock(LlmEvent::class);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable->method('getLlmEvent')->with($llmEventId)->willReturn($existentLlmEvent);
        $llmEventsTable->expects($this->once())->method('deleteLlmEvent')->with($llmEventId);
        $this->serviceManager->setService(LlmEventsTable::class, $llmEventsTable);

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/llm-event/' . $llmEventId, 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $expectedData = [
            'result' => [
                'message' => 'Event ' . $llmEventId . ' has been successfully deleted',
            ],
            'error' => null,
        ];

        $this->assertSame($expectedData, $response);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDeleteWhenNoPermissions(): void
    {
        $llmEventId = $this->faker->numberBetween(1, 100);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('admin/llm-event/' . $llmEventId, 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'Method is available for global admins only',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(401);
    }
}
