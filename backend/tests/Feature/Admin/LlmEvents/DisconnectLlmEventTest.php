<?php

declare(strict_types=1);

namespace tests\Feature\Admin\LlmEvents;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Entity\LlmEvent\LlmEvent as CompanyLlmEvent;
use STCompany\Entity\Role;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;
use tests\Feature\AuthTestCase;

final class DisconnectLlmEventTest extends AuthTestCase
{
    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testDisconnect(): void
    {
        $llmEventId = $this->faker->numberBetween(1, 100);

        $existentLlmEvent = $this->createMock(LlmEvent::class);
        $existentCompanyLlmEvent = $this->createMock(CompanyLlmEvent::class);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId)
            ->willReturn($existentLlmEvent);
        $this->serviceManager->setService(LlmEventsTable::class, $llmEventsTable);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId, $this->companyId)
            ->willReturn($existentCompanyLlmEvent);
        $companyLlmEventsTable
            ->expects($this->once())
            ->method('deleteEvent')
            ->with($llmEventId, $this->companyId);
        $this->serviceManager->setService(CompanyLlmEventsTable::class, $companyLlmEventsTable);

        $this->loginAs(Role::ADMIN_ROLE_TYPE);

        $this->dispatchApi('admin/llm-event/' . $llmEventId . '/disconnect', 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $expectedData = [
            'result' => [
                'is_deleted' => true,
                'message' => 'Successfully disconnected event from company.',
            ],
            'error' => null,
        ];

        $this->assertSame($expectedData, $response);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDisconnectWhenNoPermissions(): void
    {
        $llmEventId = $this->faker->numberBetween(1, 100);

        $this->loginAs(Role::COMPANY_ADMIN_ROLE_TYPE);
        $this->dispatchApi('admin/llm-event/' . $llmEventId . '/disconnect', 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'Method is available for global admins only',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(401);
    }
}
