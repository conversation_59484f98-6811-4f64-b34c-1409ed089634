<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Webhooks;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use Psr\Container\ContainerExceptionInterface;
use ST<PERSON>pi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompaniesWebhooksSettingsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Role;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use stdClass;
use tests\Feature\AuthTestCase;

final class UpdateWebhookSettingsTest extends AuthTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     * @throws ContainerExceptionInterface
     */
    public function testUpdate(): void
    {
        $webhookId = $this->faker->numberBetween(1, 100);
        $webhookType = $this->faker->word();
        $isEnabled = $this->faker->boolean();
        $url = $this->faker->url();
        $headers = [$this->faker->word() => $this->faker->word()];

        $webhookSettingsData = [
            'company_webhook_setting_id' => $webhookId,
            'type' => $webhookType,
            'is_enabled' => (int) $isEnabled,
            'url' => $url,
            'headers' => json_encode($headers),
            'company_id' => $this->companyId,
            'some_another_field' => 'some value',
        ];
        $webhookSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $webhookSettingsTable
            ->expects($this->once())
            ->method('updateWebhookSettings')
            ->with($webhookId, $url, $headers, $isEnabled);

        $webhookSettingsTable
            ->expects($this->exactly(2))
            ->method('getWebhookSettingsData')
            ->with($webhookId, $this->companyId)
            ->willReturn($webhookSettingsData);
        $this->serviceManager->setService(CompaniesWebhooksSettingsTable::class, $webhookSettingsTable);

        $this->serviceManager->setService(
            WebhookSettingsSelector::class,
            $this->serviceManager->build(WebhookSettingsSelector::class)
        );

        $this->getRequest()->setContent(json_encode([
            'is_enabled' => $isEnabled,
            'url' => $url,
            'headers' => $headers,
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::WEBHOOKS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('company/webhook/' . $webhookId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $expectedData = [
            'id' => $webhookId,
            'type' => $webhookType,
            'is_enabled' => $isEnabled,
            'url' => $url,
            'headers' => $headers,
            'company_id' => $this->companyId
        ];
        $this->assertSame($expectedData, $response['result']['webhook']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenNoHeaders(): void
    {
        $webhookId = $this->faker->numberBetween(1, 100);
        $webhookType = $this->faker->word();
        $isEnabled = $this->faker->boolean();
        $url = $this->faker->url();
        $headers = [];

        $webhookSettingsData = [
            'company_webhook_setting_id' => $webhookId,
            'type' => $webhookType,
            'is_enabled' => (int) $isEnabled,
            'url' => $url,
            'headers' => json_encode($headers),
            'company_id' => $this->companyId,
            'some_another_field' => 'some value',
        ];
        $webhookSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $webhookSettingsTable
            ->expects($this->once())
            ->method('updateWebhookSettings')
            ->with($webhookId, $url, $headers, $isEnabled);

        $webhookSettingsTable
            ->expects($this->exactly(2))
            ->method('getWebhookSettingsData')
            ->with($webhookId, $this->companyId)
            ->willReturn($webhookSettingsData);
        $this->serviceManager->setService(CompaniesWebhooksSettingsTable::class, $webhookSettingsTable);

        $this->serviceManager->setService(
            WebhookSettingsSelector::class,
            $this->serviceManager->build(WebhookSettingsSelector::class)
        );

        $this->getRequest()->setContent(json_encode([
            'is_enabled' => $isEnabled,
            'url' => $url,
            'headers' => $headers,
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::WEBHOOKS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('company/webhook/' . $webhookId, 'PUT');

        $response = json_decode($this->getResponse()->getContent());

        $this->assertSame($webhookId, $response->result->webhook->id);
        $this->assertSame($webhookType, $response->result->webhook->type);
        $this->assertSame($isEnabled, $response->result->webhook->is_enabled);
        $this->assertSame($url, $response->result->webhook->url);
        $this->assertEquals(new stdClass(), $response->result->webhook->headers);
        $this->assertEquals($this->companyId, $response->result->webhook->company_id);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenWebhookDoesntExist(): void
    {
        $webhookId = $this->faker->numberBetween(1, 100);

        $webhookSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $webhookSettingsTable
            ->expects($this->once())
            ->method('getWebhookSettingsData')
            ->with($webhookId, $this->companyId)
            ->willThrowException(new NotFoundApiException());
        $this->serviceManager->setService(CompaniesWebhooksSettingsTable::class, $webhookSettingsTable);

        $this->serviceManager->setService(
            WebhookSettingsSelector::class,
            $this->serviceManager->build(WebhookSettingsSelector::class)
        );

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('company/webhook/' . $webhookId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('The webhook does not exists.', $response['error']['messages']['id'][0]);

        $this->assertResponseStatusCode(422);
    }

    /**
     * @dataProvider wrongDataDataProvider
     * @param mixed $isEnabled
     * @param string|null $url
     * @param mixed $headers
     * @param string $error
     * @param string $fieldName
     * @return void
     * @throws ContainerExceptionInterface
     * @throws PHPUnitException
     */
    public function testUpdateWhenWrongData(
        mixed $isEnabled,
        ?string $url,
        mixed $headers,
        string $error,
        string $fieldName
    ): void {
        $webhookId = $this->faker->numberBetween(1, 100);

        $webhookSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $this->serviceManager->setService(CompaniesWebhooksSettingsTable::class, $webhookSettingsTable);

        $this->serviceManager->setService(
            WebhookSettingsSelector::class,
            $this->serviceManager->build(WebhookSettingsSelector::class)
        );

        $this->getRequest()->setContent(json_encode([
            'is_enabled' => $isEnabled,
            'url' => $url,
            'headers' => $headers,
        ]));

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('company/webhook/' . $webhookId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($error, $response['error']['messages'][$fieldName][0]);

        $this->assertResponseStatusCode(422);
    }

    public static function wrongDataDataProvider(): array
    {
        return [
            [
                'isEnabled' => 'some wrong is enabled',
                'url' => 'some-url.com',
                'headers' => ['header' => 'value'],
                'error' => 'Wrong is enabled.',
                'fieldName' => 'is_enabled'
            ],
            [
                'isEnabled' => true,
                'url' => null,
                'headers' => ['header' => 'value'],
                'error' => 'Url must be not empty and less than 1024 symbols',
                'fieldName' => 'url'
            ],
            [
                'isEnabled' => true,
                'url' => str_repeat('a', 1025),
                'headers' => ['header' => 'value'],
                'error' => 'Url must be not empty and less than 1024 symbols',
                'fieldName' => 'url'
            ],
            [
                'isEnabled' => true,
                'url' => 'some-url.com',
                'headers' => 'some wrong headers',
                'error' => 'Headers must be an array.',
                'fieldName' => 'headers'
            ],
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenReadPermissions(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::WEBHOOKS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('company/webhook/' . $this->faker->numberBetween(1, 100), 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateWhenNoPermissions(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('company/webhook/' . $this->faker->numberBetween(1, 100), 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
