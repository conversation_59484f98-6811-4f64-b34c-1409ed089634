<?php

declare(strict_types=1);

namespace tests\Feature\Companies\LlmEvents;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\LlmEvent\LlmEvent as CompanyLlmEvent;
use STCompany\Entity\Role;
use STLlmEvent\Data\LlmEventsTable;
use STLlmEvent\Entity\LlmEvent;
use tests\Feature\AuthTestCase;

final class DisconnectLlmEventTest extends AuthTestCase
{
    /**
     * @throws Exception
     * @throws PHPUnitException
     */
    public function testDisconnect(): void
    {
        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(true);

        $llmEventId = $this->faker->numberBetween(1, 100);

        $existentLlmEvent = $this->createMock(LlmEvent::class);
        $existentCompanyLlmEvent = $this->createMock(CompanyLlmEvent::class);

        $llmEventsTable = $this->createMock(LlmEventsTable::class);
        $llmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId)
            ->willReturn($existentLlmEvent);
        $this->serviceManager->setService(LlmEventsTable::class, $llmEventsTable);

        $companyLlmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $companyLlmEventsTable
            ->method('getLlmEvent')
            ->with($llmEventId, $this->companyId)
            ->willReturn($existentCompanyLlmEvent);
        $companyLlmEventsTable
            ->expects($this->once())
            ->method('deleteEvent')
            ->with($llmEventId, $this->companyId);
        $this->serviceManager->setService(CompanyLlmEventsTable::class, $companyLlmEventsTable);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('company/llm-event/' . $llmEventId . '/disconnect', 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $expectedData = [
            'result' => [
                'is_deleted' => true,
                'message' => 'Successfully disconnected event from company.',
            ],
            'error' => null,
        ];

        $this->assertSame($expectedData, $response);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDisconnectWhenManageDisabled(): void
    {
        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(false);

        $llmEventId = $this->faker->numberBetween(1, 100);

        $this->loginAs(Role::COMPANY_ADMIN_ROLE_TYPE);
        $this->dispatchApi('company/llm-event/' . $llmEventId . '/disconnect', 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'LLM event manage is currently disabled for your company. Contact support for assistance.',
            $response['error']['message']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDisconnectWhenReadPermission(): void
    {
        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(true);

        $llmEventId = $this->faker->numberBetween(1, 100);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::EVENTS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('company/llm-event/' . $llmEventId . '/disconnect', 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDisconnectWhenNoPermissions(): void
    {
        $this->company->method('isManageLlmEventsByUsersEnabled')->willReturn(true);

        $llmEventId = $this->faker->numberBetween(1, 100);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('company/llm-event/' . $llmEventId . '/disconnect', 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
