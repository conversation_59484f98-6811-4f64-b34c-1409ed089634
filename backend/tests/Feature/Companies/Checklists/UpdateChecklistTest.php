<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Checklists;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STCompany\Data\ChecklistsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Checklist\Checklist;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class UpdateChecklistTest extends AuthTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdate(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);
        $checklistName = $this->faker->word();
        $callDurationThreshold = $this->faker->numberBetween(1, 500);

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $checklistsTable
            ->expects($this->once())
            ->method('saveChecklist')
            ->with(
                self::callback(
                    function (Checklist $checklist) use ($checklistId, $checklistName, $callDurationThreshold) {
                        return $checklist->getId() === $checklistId
                            && $checklist->getName() === $checklistName
                            && $checklist->getCallDurationThreshold() === $callDurationThreshold
                            && $checklist->getCompanyId() === $this->companyId;
                    }
                ),
            );
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $this->getRequest()->setContent(json_encode([
            'checklist_name' => $checklistName,
            'call_duration_threshold' => $callDurationThreshold
        ]));

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::FLOWS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('checklist/' . $checklistId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame($this->companyId, $response['result']['checklist']['company_id']);
        $this->assertSame($checklistId, $response['result']['checklist']['id']);
        $this->assertSame($checklistName, $response['result']['checklist']['name']);
        $this->assertSame($callDurationThreshold, $response['result']['checklist']['call_duration_threshold']);
        $this->assertResponseStatusCode(200);
    }

    /**
     * @dataProvider adminRoleTypesDataProvider
     * @param int $adminRoleType
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testUpdateByAdmin(int $adminRoleType): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);
        $checklistName = $this->faker->word();
        $callDurationThreshold = $this->faker->numberBetween(1, 500);

        $checklistsTable = $this->createMock(ChecklistsTable::class);
        $this->serviceManager->setService(ChecklistsTable::class, $checklistsTable);

        $this->getRequest()->setContent(json_encode([
            'checklist_name' => $checklistName,
            'call_duration_threshold' => $callDurationThreshold
        ]));

        $this->loginAs($adminRoleType);
        $this->dispatchApi('checklist/' . $checklistId, 'PUT');

        $this->assertResponseStatusCode(200);
    }

    public static function adminRoleTypesDataProvider(): array
    {
        return [
            [1], // Role::ADMIN_ROLE_TYPE
            [4], // Role::COMPANY_ADMIN_ROLE_TYPE
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenReadPermission(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::FLOWS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('checklist/' . $checklistId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testCreateWhenNoPermissions(): void
    {
        $checklistId = $this->faker->numberBetween(1, 100);

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('checklist/' . $checklistId, 'PUT');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
