<?php

declare(strict_types=1);

namespace tests\Feature\Calls\Reanalyze;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use Psr\Container\ContainerExceptionInterface;
use STCall\Data\CallsAlgoEventsTable;
use STCall\Data\CallsChecklistsPointsTable;
use STCall\Data\CallsParagraphsTable;
use STCall\Data\CallsSummarizationsTable;
use STCall\Data\CallsTable;
use STCall\Data\EventHappeningsChangesTable;
use STCall\Entity\Call;
use STCall\Service\CallAnalysisService;
use STCall\Service\CallService;
use STCompany\Entity\Role;
use tests\Feature\AuthTestCase;

final class ReanalyzeTest extends AuthTestCase
{
    /**
     * @throws PHPUnitException
     * @throws Exception
     * @throws ContainerExceptionInterface
     */
    public function testReanalyzeWhenReanalyzeDoesntFinish(): void
    {
        $callId = $this->faker->uuid();
        $nextQueueName = $this->faker->word();

        $callData = [
            [
                'call_id' => $callId,
                'company_id' => $this->companyId,
                'is_analyzed' => false,
            ]
        ];
        $callsTable = $this->createMock(CallsTable::class);
        $callsTable
            ->method('getCall')
            ->with([$callId], $this->companyId, allowRemoved: false)
            ->willReturn($callData);
        $this->serviceManager->setService(CallsTable::class, $callsTable);

        $this->serviceManager->setService(
            CallService::class,
            $this->serviceManager->build(CallService::class)
        );

        $callAnalysisService = $this->createMock(CallAnalysisService::class);
        $callAnalysisService
            ->method('getNextAnalysisStep')
            ->with(
                self::callback(
                    function (Call $call) use ($callId) {
                        return $callId === $call->getId();
                    }
                ),
            )
            ->willReturn($nextQueueName);
        $callAnalysisService
            ->expects($this->once())
            ->method('addToQueue')
            ->with(
                $this->companyId,
                self::callback(
                    function (Call $call) use ($callId) {
                        return $callId === $call->getId();
                    }
                ),
                $nextQueueName
            );
        $this->serviceManager->setService(CallAnalysisService::class, $callAnalysisService);

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('call/' . $callId . '/reanalyze', 'PATCH');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('scheduled', $response['result']['status']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testReanalyzeWhenReanalyzeFinished(): void
    {
        $callId = $this->faker->uuid();
        $nextQueueName = $this->faker->word();

        $callData = [
            [
                'call_id' => $callId,
                'company_id' => $this->companyId,
                'call_language' => $this->faker->languageCode(),
                'is_translated' => true,
                'is_transcribed' => true,
                'is_analyzed' => true,
                'is_speakers_roles_detected' => true,
                'is_checklist_completed' => true,
                'is_summarization_completed' => true,
                'is_llm_events_detected' => true,
            ]
        ];
        $callsTable = $this->createMock(CallsTable::class);
        $callsTable
            ->method('getCall')
            ->with([$callId], $this->companyId, allowRemoved: false)
            ->willReturn($callData);
        $callsTable
            ->expects($this->once())
            ->method('saveCall')
            ->with(self::callback(
                function (Call $call) use ($callId) {
                    return $callId === $call->getId()
                        && is_null($call->getLanguage())
                        && $call->isTranslated() === false
                        && $call->isTranscribed() === false
                        && $call->isAnalyzed() === false
                        && $call->isSpeakersRolesDetected() === false
                        && $call->isChecklistCompleted() === false
                        && $call->isSummarizationCompleted() === false
                        && $call->isLlmEventsDetected() === false
                        ;
                }
            ));
        $this->serviceManager->setService(CallsTable::class, $callsTable);

        $callsParagraphsTable = $this->createMock(CallsParagraphsTable::class);
        $callsParagraphsTable
            ->expects($this->once())
            ->method('deleteByCall')
            ->with(self::callback(
                function (Call $call) use ($callId) {
                    return $callId === $call->getId();
                }
            ));
        $this->serviceManager->setService(CallsParagraphsTable::class, $callsParagraphsTable);

        $callsAlgoEventsTable = $this->createMock(CallsAlgoEventsTable::class);
        $callsAlgoEventsTable
            ->expects($this->once())
            ->method('deleteByCall')
            ->with(self::callback(
                function (Call $call) use ($callId) {
                    return $callId === $call->getId();
                }
            ));
        $this->serviceManager->setService(CallsAlgoEventsTable::class, $callsAlgoEventsTable);

        $eventHappeningsChangesTable = $this->createMock(EventHappeningsChangesTable::class);
        $eventHappeningsChangesTable
            ->expects($this->once())
            ->method('deleteByCall')
            ->with(self::callback(
                function (Call $call) use ($callId) {
                    return $callId === $call->getId();
                }
            ));
        $this->serviceManager->setService(EventHappeningsChangesTable::class, $eventHappeningsChangesTable);

        $callsChecklistsPointsTable = $this->createMock(CallsChecklistsPointsTable::class);
        $callsChecklistsPointsTable
            ->expects($this->once())
            ->method('deleteByCall')
            ->with(self::callback(
                function (Call $call) use ($callId) {
                    return $callId === $call->getId();
                }
            ));
        $this->serviceManager->setService(CallsChecklistsPointsTable::class, $callsChecklistsPointsTable);

        $callsSummarizationsTable = $this->createMock(CallsSummarizationsTable::class);
        $callsSummarizationsTable
            ->expects($this->once())
            ->method('removeCallSummarization')
            ->with($callId, $this->companyId);
        $this->serviceManager->setService(CallsSummarizationsTable::class, $callsSummarizationsTable);

        $this->serviceManager->setService(
            CallService::class,
            $this->serviceManager->build(CallService::class)
        );

        $callAnalysisService = $this->createMock(CallAnalysisService::class);
        $callAnalysisService
            ->method('getNextAnalysisStep')
            ->with(
                self::callback(
                    function (Call $call) use ($callId) {
                        return $callId === $call->getId();
                    }
                ),
            )
            ->willReturn($nextQueueName);
        $callAnalysisService
            ->expects($this->once())
            ->method('addToQueue')
            ->with(
                $this->companyId,
                self::callback(
                    function (Call $call) use ($callId) {
                        return $callId === $call->getId();
                    }
                ),
                $nextQueueName
            );
        $this->serviceManager->setService(CallAnalysisService::class, $callAnalysisService);

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('call/' . $callId . '/reanalyze', 'PATCH');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('scheduled', $response['result']['status']);

        $this->assertResponseStatusCode(200);
    }
}
