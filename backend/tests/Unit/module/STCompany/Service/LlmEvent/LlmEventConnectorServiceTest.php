<?php

namespace tests\Unit\module\STCompany\Service\LlmEvent;

use PHPUnit\Framework\MockObject\Exception;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Service\LlmEvent\LlmEventConnectorService;
use tests\TestCase;

class LlmEventConnectorServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testDisconnect(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $eventId = $this->faker->numberBetween(101, 200);

        $llmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $llmEventsTable
            ->expects($this->once())
            ->method('deleteEvent')
            ->with($eventId, $companyId);

        $connector = new LlmEventConnectorService($llmEventsTable);
        $connector->disconnect($eventId, $companyId);
    }
}
