<?php

namespace tests\Unit\module\STCompany\Service\LlmEvent;

use PHPUnit\Framework\MockObject\Exception;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Entity\LlmEvent\LlmEvent;
use STCompany\Entity\LlmEvent\LlmEventCollection;
use STCompany\Service\LlmEvent\LlmEventSelectorService;
use tests\TestCase;

class LlmEventSelectorServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetLlmEvents(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);

        $name1 = $this->faker->word();
        $name2 = $this->faker->word();
        $description1 = $this->faker->text();
        $description2 = $this->faker->text();

        $llmEvent1 = new LlmEvent();
        $llmEvent1->setName($name1);
        $llmEvent1->setDescription($description1);

        $llmEvent2 = new LlmEvent();
        $llmEvent2->setName($name2);
        $llmEvent2->setDescription($description2);

        $llmEvents = [$llmEvent1, $llmEvent2];

        $llmEventsCollection = new LlmEventCollection($llmEvents);

        $llmEventsTable = $this->createMock(CompanyLlmEventsTable::class);
        $llmEventsTable
            ->method('getLlmEvents')
            ->with($companyId)
            ->willReturn($llmEventsCollection);

        $selector = new LlmEventSelectorService($llmEventsTable);

        $this->assertSame($llmEventsCollection, $selector->getLlmEvents($companyId));
    }
}
