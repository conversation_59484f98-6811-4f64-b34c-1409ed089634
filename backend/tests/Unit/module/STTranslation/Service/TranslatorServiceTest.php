<?php

declare(strict_types=1);

namespace tests\Unit\module\STTranslation\Service;

use GuzzleHttp\Exception\GuzzleException;
use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\ThirdPartyApiException;
use STTranslation\Service\Drivers\DriverInterface;
use STTranslation\Service\LanguageDetector;
use STTranslation\Service\TextCombiner;
use STTranslation\Service\TranslatorDriverFactory;
use STTranslation\Service\TranslatorService;
use tests\TestCase;

final class TranslatorServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testNeedBatchTranslateWhenDriverSupportsBatchTranslate(): void
    {
        $textCombiner = $this->createMock(TextCombiner::class);
        $languageDetector = $this->createMock(LanguageDetector::class);

        $driverName = $this->faker->word();
        $driver = $this->createMock(DriverInterface::class);
        $driver
            ->method('isSupportBatchTranslation')
            ->willReturn(true);
        $translatorDriverFactory = $this->createMock(TranslatorDriverFactory::class);
        $translatorDriverFactory
            ->method('createDriver')
            ->with($driverName)
            ->willReturn($driver);

        $translator = new TranslatorService($textCombiner, $translatorDriverFactory, $languageDetector);

        $this->assertTrue($translator->needBatchTranslate($driverName));
    }

    /**
     * @throws Exception
     */
    public function testNeedBatchTranslateWhenDriverDoesntSupportsBatchTranslateAndNeedTextCombine(): void
    {
        $textCombiner = $this->createMock(TextCombiner::class);
        $languageDetector = $this->createMock(LanguageDetector::class);

        $driverName = $this->faker->word();
        $driver = $this->createMock(DriverInterface::class);
        $driver
            ->method('isSupportBatchTranslation')
            ->willReturn(false);
        $driver
            ->method('needTextsCombine')
            ->willReturn(true);
        $translatorDriverFactory = $this->createMock(TranslatorDriverFactory::class);
        $translatorDriverFactory
            ->method('createDriver')
            ->with($driverName)
            ->willReturn($driver);

        $translator = new TranslatorService($textCombiner, $translatorDriverFactory, $languageDetector);

        $this->assertTrue($translator->needBatchTranslate($driverName));
    }

    /**
     * @return void
     * @throws GuzzleException
     * @throws ThirdPartyApiException
     * @throws Exception
     */
    public function testTranslateBatchWhenDriverDoesntSupportIt(): void
    {
        $maxTextLength = 100;
        $languageCode = $this->faker->languageCode();

        $driverName = $this->faker->word();
        $driver = $this->createMock(DriverInterface::class);
        $driver
            ->method('isSupportBatchTranslation')
            ->willReturn(false);
        $driver
            ->method('getMaxTextLength')
            ->willReturn($maxTextLength);

        $translatorDriverFactory = $this->createMock(TranslatorDriverFactory::class);
        $translatorDriverFactory
            ->method('createDriver')
            ->with($driverName)
            ->willReturn($driver);

        $textsToTranslate = [
            $this->faker->text(60),
            $this->faker->text(10),
            $this->faker->text(50),
        ];

        $combinedTextsToTranslate = [
            $this->faker->text(100),
            $this->faker->text(20),
        ];

        $combinedTranslatedText1 = $this->faker->text(100);
        $combinedTranslatedText2 = $this->faker->text(20);

        $textCombiner = $this->createMock(TextCombiner::class);
        $textCombiner
            ->method('combine')
            ->with($textsToTranslate, $maxTextLength)
            ->willReturn($combinedTextsToTranslate);

        $translateMap = [
            [$combinedTextsToTranslate[0], $driverName, $combinedTranslatedText1],
            [$combinedTextsToTranslate[1], $driverName, $combinedTranslatedText2],
        ];

        $languageDetector = $this->createMock(LanguageDetector::class);

        $translator = $this->getMockBuilder(TranslatorService::class)
            ->setConstructorArgs([
                $textCombiner,
                $translatorDriverFactory,
                $languageDetector
            ])
            ->onlyMethods(['translate'])
            ->getMock();
        $translator
            ->method('translate')
            ->willReturnMap($translateMap);

        $translatedTexts = [
            $this->faker->text(60),
            $this->faker->text(10),
            $this->faker->text(50),
        ];

        $textCombiner
            ->method('split')
            ->with([$combinedTranslatedText1, $combinedTranslatedText2])
            ->willReturn($translatedTexts);

        $this->assertSame($translatedTexts, $translator->translateBatch($textsToTranslate, $driverName, $languageCode));
    }

    /**
     * @throws Exception
     * @throws ThirdPartyApiException
     * @throws GuzzleException
     */
    public function testTranslateBatchWhenDriverSupportIt(): void
    {
        $languageCode = $this->faker->languageCode();

        $textsToTranslate = [
            $this->faker->text(60),
            $this->faker->text(10),
            $this->faker->text(50),
        ];

        $translatedTexts = [
            $this->faker->text(60),
            $this->faker->text(10),
            $this->faker->text(50),
        ];

        $driverName = $this->faker->word();
        $driver = $this->createMock(DriverInterface::class);
        $driver
            ->method('isSupportBatchTranslation')
            ->willReturn(true);
        $driver
            ->method('translateBatch')
            ->with($textsToTranslate, $languageCode)
            ->willReturn($translatedTexts);

        $translatorDriverFactory = $this->createMock(TranslatorDriverFactory::class);
        $translatorDriverFactory
            ->method('createDriver')
            ->with($driverName)
            ->willReturn($driver);

        $textCombiner = $this->createMock(TextCombiner::class);
        $languageDetector = $this->createMock(LanguageDetector::class);

        $translator = new TranslatorService($textCombiner, $translatorDriverFactory, $languageDetector);

        $this->assertSame($translatedTexts, $translator->translateBatch($textsToTranslate, $driverName, $languageCode));
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     */
    public function testDetectLanguage(): void
    {
        $text = $this->faker->text();
        $languageCode = $this->faker->languageCode();

        $languageDetector = $this->createMock(LanguageDetector::class);
        $languageDetector->method('detectLanguageByText')->with($text)->willReturn($languageCode);

        $textCombiner = $this->createMock(TextCombiner::class);
        $translatorDriverFactory = $this->createMock(TranslatorDriverFactory::class);

        $translator = new TranslatorService($textCombiner, $translatorDriverFactory, $languageDetector);

        $this->assertSame($languageCode, $translator->detectLanguage($text));
    }

    /**
     * @dataProvider emptyTextDataProvider
     * @param string|null $text
     * @return void
     * @throws Exception
     * @throws GuzzleException
     */
    public function testDetectLanguageWhenEmptyText(?string $text): void
    {
        $textCombiner = $this->createMock(TextCombiner::class);
        $translatorDriverFactory = $this->createMock(TranslatorDriverFactory::class);
        $languageDetector = $this->createMock(LanguageDetector::class);

        $translator = new TranslatorService($textCombiner, $translatorDriverFactory, $languageDetector);

        $this->assertSame('en', $translator->detectLanguage($text));
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     */
    public function testDetectContentLanguage(): void
    {
        $content = base64_decode($this->faker->text());
        $languageCode = $this->faker->languageCode();

        $languageDetector = $this->createMock(LanguageDetector::class);
        $languageDetector->method('detectLanguageByContent')->with($content)->willReturn($languageCode);

        $textCombiner = $this->createMock(TextCombiner::class);
        $translatorDriverFactory = $this->createMock(TranslatorDriverFactory::class);

        $translator = new TranslatorService($textCombiner, $translatorDriverFactory, $languageDetector);

        $this->assertSame($languageCode, $translator->detectContentLanguage($content));
    }

    public static function emptyTextDataProvider(): array
    {
        return [
            [''],
            [null],
        ];
    }
}
