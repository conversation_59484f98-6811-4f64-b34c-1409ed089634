<?php

namespace tests\Unit\module\STRoboTruck\Service;

use Laminas\Http\Client;
use Laminas\Http\Response;
use PHPUnit\Framework\MockObject\Exception;
use Rs\JsonLines\Exception\InvalidJson;
use Rs\JsonLines\Exception\NonTraversable;
use RuntimeException;
use STRoboTruck\Service\DataCollection\DataCollector;
use STRoboTruck\Service\HttpClient\ClientFactory;
use STRoboTruck\Service\PusherService;
use tests\TestCase;

class PusherServiceTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws InvalidJson
     * @throws NonTraversable
     */
    public function testPush(): void
    {
        $client = $this->createMock(Client::class);

        $events = [
            [
                'name' => 'search client',
                'source' => 'robonote',
            ],
            [
                'name' => 'search call',
                'source' => 'frontend',
            ]
        ];
        $jsonlEvents = '{"name":"search client","source":"robonote"}
{"name":"search call","source":"frontend"}
';
        $client
            ->expects($this->once())
            ->method('setRawBody')
            ->with($jsonlEvents);

        $response = $this->createMock(Response::class);
        $response
            ->method('isSuccess')
            ->willReturn(true);
        $client
            ->expects($this->once())
            ->method('send')
            ->willReturn($response);

        $clientFactory = $this->createMock(ClientFactory::class);
        $clientFactory
            ->method('create')
            ->willReturn($client);

        $dataCollector = $this->createMock(DataCollector::class);

        $pusher = new PusherService($clientFactory, $dataCollector);
        $pusher->push($events);
    }

    /**
     * @return void
     * @throws Exception
     * @throws InvalidJson
     * @throws NonTraversable
     */
    public function testPushWhenUnsuccessfulResponse(): void
    {
        $client = $this->createMock(Client::class);

        $events = [['key' => 'value']];

        $statusCode = $this->faker->numberBetween(100, 600);
        $responseBody = $this->faker->text();
        $response = $this->createMock(Response::class);
        $response
            ->method('isSuccess')
            ->willReturn(false);
        $response
            ->method('getStatusCode')
            ->willReturn($statusCode);
        $response
            ->method('getBody')
            ->willReturn($responseBody);
        $client
            ->expects($this->once())
            ->method('send')
            ->willReturn($response);

        $clientFactory = $this->createMock(ClientFactory::class);
        $clientFactory
            ->method('create')
            ->willReturn($client);

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collectError')
            ->with($events, $statusCode, $responseBody);

        $pusher = new PusherService($clientFactory, $dataCollector);
        $pusher->push($events);
    }

    /**
     * @return void
     * @throws Exception
     * @throws InvalidJson
     * @throws NonTraversable
     */
    public function testPushWhenSendError(): void
    {
        $client = $this->createMock(Client::class);

        $events = [['key' => 'value']];

        $code = $this->faker->numberBetween(0, 1000);
        $error = 'some error';
        $exception = new RuntimeException($error, $code);
        $client
            ->expects($this->once())
            ->method('send')
            ->willThrowException($exception);

        $clientFactory = $this->createMock(ClientFactory::class);
        $clientFactory
            ->method('create')
            ->willReturn($client);

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collectError')
            ->with($events, $code, $error);

        $pusher = new PusherService($clientFactory, $dataCollector);
        $pusher->push($events);
    }
}
