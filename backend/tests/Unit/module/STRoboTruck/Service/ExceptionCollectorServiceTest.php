<?php

namespace tests\Unit\module\STRoboTruck\Service;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STLib\IdGenerator\IdGeneratorService;
use STRoboTruck\Service\DataCollection\DataCollector;
use STRoboTruck\Service\ExceptionCollectorService;
use tests\TestCase;

class ExceptionCollectorServiceTest extends TestCase
{
    /**
     * @throws PHPUnitException
     */
    public function testCollect(): void
    {
        $roboTruckEventName = 'exception';

        $error = $this->faker->text(20);
        $exception = new Exception($error);

        $someExceptionId = $this->faker->text();
        $exceptionIdGenerator = $this->createMock(IdGeneratorService::class);
        $exceptionIdGenerator
            ->method('generatePseudoUniqueId')
            ->willReturn($someExceptionId);

        $roboTruckEventMessage = Exception::class;
        $roboTruckEventExtra = [
            'exception_id' => $someExceptionId,
            'error_message' => $error,
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'stack_trace' => $exception->getTrace(),
        ];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra);

        $exceptionCollector = new ExceptionCollectorService($exceptionIdGenerator, $dataCollector);
        $exceptionCollector->collect($exception);
    }

    /**
     * @throws PHPUnitException
     */
    public function testCollectWhenPassedExceptionId(): void
    {
        $roboTruckEventName = 'exception';

        $error = $this->faker->text(20);
        $exception = new Exception($error);

        $someExceptionId = $this->faker->numberBetween(-1, 100);
        $exceptionIdGenerator = $this->createMock(IdGeneratorService::class);

        $roboTruckEventMessage = Exception::class;
        $roboTruckEventExtra = [
            'exception_id' => $someExceptionId,
            'error_message' => $error,
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'stack_trace' => $exception->getTrace(),
        ];

        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->once())
            ->method('collect')
            ->with($roboTruckEventName, $roboTruckEventMessage, $roboTruckEventExtra);

        $exceptionCollector = new ExceptionCollectorService($exceptionIdGenerator, $dataCollector);
        $exceptionCollector->collect($exception, $someExceptionId);
    }
}
