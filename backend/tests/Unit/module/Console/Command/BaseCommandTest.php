<?php

namespace tests\Unit\module\Console\Command;

use Console\Command\BaseCommand;
use Lam<PERSON>\Cli\Input\ParamAwareInputInterface;
use <PERSON>inas\Mvc\Controller\PluginManager;
use PHPUnit\Framework\MockObject\Exception;
use RuntimeException;
use STRoboTruck\Controller\Plugin\RoboTruck;
use STRoboTruck\Service\ExceptionCollectorService;
use Symfony\Component\Console\Exception\ExceptionInterface;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use tests\TestCase;
use Throwable;

class BaseCommandTest extends TestCase
{
    /**
     * @throws ExceptionInterface
     * @throws Exception
     */
    public function testLaunch(): void
    {
        $exception = new RuntimeException();

        $command = new class($exception) extends BaseCommand {
            public function __construct(private readonly Throwable $throwable)
            {
                parent::__construct();
            }

            protected function launch(InputInterface $input, OutputInterface $output): int
            {
                throw $this->throwable;
            }
        };

        $input = $this->createMock(ParamAwareInputInterface::class);
        $output = $this->createMock(OutputInterface::class);

        $exceptionCollector = $this->createMock(ExceptionCollectorService::class);
        $exceptionCollector
            ->expects($this->once())
            ->method('collect')
            ->with($exception);

        $roboTruckPlugin = $this->createMock(RoboTruck::class);
        $roboTruckPlugin
            ->method('exceptionCollector')
            ->willReturn($exceptionCollector);

        $pluginManager = $this->createMock(PluginManager::class);
        $pluginManager
            ->method('get')
            ->with('roboTruck')
            ->willReturn($roboTruckPlugin);

        $command->setServiceManager($this->serviceManager);
        $command->setPluginManager($pluginManager);

        $this->expectException(RuntimeException::class);

        $command->run($input, $output);
    }
}
