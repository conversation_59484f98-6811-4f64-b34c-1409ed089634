<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\ParamsBuilding;

use STAlgo\Service\ParamsBuilding\RequestParams;
use tests\TestCase;

class RequestParamsTest extends TestCase
{
    public function testToArray(): void
    {
        $params = [
            $this->faker->word() => $this->faker->text(),
            $this->faker->word() => $this->faker->text(),
            $this->faker->word() => $this->faker->text(),
        ];

        $requestParams = new RequestParams($params);
        $this->assertSame($params, $requestParams->toArray());
    }

    public function testAddParams(): void
    {
        $params = [$this->faker->word() => $this->faker->text()];

        $key = 'key';
        $addParams = [$this->faker->word() => $this->faker->text()];

        $requestParams = new RequestParams($params);
        $requestParams->addParams($key, $addParams);

        $expectedParams = array_merge($params, [$key => $addParams]);

        $this->assertSame($expectedParams, $requestParams->toArray());
    }
}
