<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\AlgoApi;

use GuzzleHttp\Exception\GuzzleException;
use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Data\AlgoApisTable;
use STAlgo\Entity\AlgoApiCollection;
use STAlgo\Service\AlgoApi\AlgoApiUpdaterService;
use STAlgo\Service\AlgoApi\Update\AiAlgoApisGetter;
use STAlgo\Service\AlgoApi\Update\AlgoApisAnalyzer;
use tests\TestCase;

final class AlgoApiUpdaterServiceTest extends TestCase
{
    /**
     * @throws Exception
     * @throws GuzzleException
     */
    public function testUpdate(): void
    {
        $providedAlgoApiCollection = $this->createMock(AlgoApiCollection::class);

        $algoApisProvider = $this->createMock(AiAlgoApisGetter::class);
        $algoApisProvider
            ->method('get')
            ->willReturn($providedAlgoApiCollection);

        $analyzedAlgoApiCollection = $this->createMock(AlgoApiCollection::class);
        $algoApisAnalyzer = $this->createMock(AlgoApisAnalyzer::class);
        $algoApisAnalyzer
            ->method('analyze')
            ->with($providedAlgoApiCollection)
            ->willReturn($analyzedAlgoApiCollection);

        $algoApisTable = $this->createMock(AlgoApisTable::class);
        $algoApisTable
            ->expects($this->once())
            ->method('saveAlgoApis')
            ->with($analyzedAlgoApiCollection);

        $updater = new AlgoApiUpdaterService($algoApisProvider, $algoApisAnalyzer, $algoApisTable);
        $updater->update();
    }
}
