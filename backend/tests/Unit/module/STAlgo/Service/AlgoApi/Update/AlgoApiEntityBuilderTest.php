<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\AlgoApi\Update;

use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Entity\AlgoApi;
use STAlgo\Entity\AlgoApiCollection;
use STAlgo\Service\AlgoApi\Update\AlgoApiEntityBuilder;
use STLib\Mvc\Hydrator\BaseHydrator;
use tests\TestCase;
use tests\WithConsecutive;

final class AlgoApiEntityBuilderTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testBuildCollection(): void
    {
        $version1 = 'v1';
        $version2 = 'v2';

        $description1 = $this->faker->text(100);
        $model1 = $this->faker->word();
        $name1 = $this->faker->word();

        $description2 = $this->faker->text(100);
        $model2 = $this->faker->word();
        $name2 = $this->faker->word();

        $algoApisData = [
            'models' => [
                [
                    'description' => $description1,
                    'model' => $model1,
                    'name' => $name1,
                    'versions' => [$version2, $version1],
                ],
                [
                    'description' => $description2,
                    'model' => $model2,
                    'name' => $name2,
                    'versions' => [$version1],
                ]
            ]
        ];

        $path1 = '/api/' . $model1 . '/' . $version2;
        $algoApiDataForHydration1 = [
            'path' => $path1,
            'industry_id' => 0,
            'analyze_method' => '',
            'name' => $name1,
            'description' => $description1,
            'is_deleted' => false,
        ];
        $path2 = '/api/' . $model1 . '/' . $version1;
        $algoApiDataForHydration2 = [
            'path' => $path2,
            'industry_id' => 0,
            'analyze_method' => '',
            'name' => $name1,
            'description' => $description1,
            'is_deleted' => false,
        ];
        $path3 = '/api/' . $model2 . '/' . $version1;
        $algoApiDataForHydration3 = [
            'path' => $path3,
            'industry_id' => 0,
            'analyze_method' => '',
            'name' => $name2,
            'description' => $description2,
            'is_deleted' => false,
        ];

        $algoApi1 = $this->createMock(AlgoApi::class);
        $algoApi1->method('getPath')->willReturn($path1);
        $algoApi2 = $this->createMock(AlgoApi::class);
        $algoApi2->method('getPath')->willReturn($path2);
        $algoApi3 = $this->createMock(AlgoApi::class);
        $algoApi3->method('getPath')->willReturn($path3);

        $algoApisCollection = new AlgoApiCollection();
        $algoApisCollection->add($algoApi1, $path1);
        $algoApisCollection->add($algoApi2, $path2);
        $algoApisCollection->add($algoApi3, $path3);

        $hydrator = $this->createMock(BaseHydrator::class);
        $hydrator
            ->method('hydrate')
            ->with(
                ...
                WithConsecutive::create(
                    [$algoApiDataForHydration1, $algoApi1],
                    [$algoApiDataForHydration2, $algoApi2],
                    [$algoApiDataForHydration3, $algoApi3],
                )
            );

        $builder = $this->getMockBuilder(AlgoApiEntityBuilder::class)
            ->onlyMethods(['createEntity'])
            ->setConstructorArgs([$hydrator])
            ->getMock();
        $builder->method('createEntity')
            ->willReturnOnConsecutiveCalls($algoApi1, $algoApi2, $algoApi3);

        $this->assertEquals($algoApisCollection, $builder->buildCollection($algoApisData));
    }
}
