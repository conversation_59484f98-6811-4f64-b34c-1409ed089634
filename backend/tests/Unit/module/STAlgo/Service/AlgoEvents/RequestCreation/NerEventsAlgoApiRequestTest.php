<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service\AlgoEvents\RequestCreation;

use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Entity\AlgoApi;
use STAlgo\Service\AlgoEvents\RequestCreation\NerEventsAlgoApiRequest;
use STAlgo\Service\ParamsBuilding\RequestParams;
use tests\TestCase;

class NerEventsAlgoApiRequestTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetEnv(): void
    {
        $env = $this->faker->word();

        $requestParams = $this->createMock(RequestParams::class);

        $algoApi = $this->createMock(AlgoApi::class);

        $request = new NerEventsAlgoApiRequest($algoApi, $this->faker->url(), $requestParams, env: $env);

        $this->assertSame($env, $request->getEnv());
    }

    /**
     * @throws Exception
     */
    public function testGetEnvWhenNoEnv(): void
    {
        $requestParams = $this->createMock(RequestParams::class);

        $algoApi = $this->createMock(AlgoApi::class);

        $request = new NerEventsAlgoApiRequest($algoApi, $this->faker->url(), $requestParams);

        $this->assertNull($request->getEnv());
    }

    /**
     * @throws Exception
     */
    public function testGetUrl(): void
    {
        $apiUrl = $this->faker->url();
        $algoApiPath = $this->faker->filePath();
        $analyzeMethod = '/' . $this->faker->word();
        $requestParams = $this->createMock(RequestParams::class);

        $algoApi = $this->createMock(AlgoApi::class);
        $algoApi
            ->method('getPath')
            ->willReturn($algoApiPath);
        $algoApi
            ->method('getAnalyzeMethod')
            ->willReturn($analyzeMethod);

        $expectedUrl = $apiUrl . $algoApiPath . $analyzeMethod;

        $request = new NerEventsAlgoApiRequest($algoApi, $apiUrl, $requestParams);
        $url = $request->getUrl();

        $this->assertSame($expectedUrl, $url);
    }

    public function testGetUrlWhenAnalyzeMethodIsSet(): void
    {
        $apiUrl = $this->faker->url();
        $algoApiPath = $this->faker->filePath();
        $anotherAnalyzeMethod = '/' . $this->faker->word();
        $algoApiAnalyzeMethod = '/' . $this->faker->word();
        $requestParams = $this->createMock(RequestParams::class);

        $algoApi = $this->createMock(AlgoApi::class);
        $algoApi
            ->method('getPath')
            ->willReturn($algoApiPath);
        $algoApi
            ->method('getAnalyzeMethod')
            ->willReturn($algoApiAnalyzeMethod);

        $expectedUrl = $apiUrl . $algoApiPath . $anotherAnalyzeMethod;

        $request = new NerEventsAlgoApiRequest($algoApi, $apiUrl, $requestParams, analyzeMethod: $anotherAnalyzeMethod);
        $url = $request->getUrl();

        $this->assertSame($expectedUrl, $url);
    }

    /**
     * @throws Exception
     */
    public function testGetParams(): void
    {
        $apiUrl = $this->faker->url();

        $requestParamsData = ['some key' => 'some value'];
        $requestParams = $this->createMock(RequestParams::class);
        $requestParams
            ->method('toArray')
            ->willReturn($requestParamsData);

        $algoApi = $this->createMock(AlgoApi::class);

        $request = new NerEventsAlgoApiRequest($algoApi, $apiUrl, $requestParams);

        $this->assertSame($requestParamsData, $request->getParams());
    }

    /**
     * @throws Exception
     */
    public function testGetAlgoApiId(): void
    {
        $algoApiId = $this->faker->numberBetween(1, 100);

        $requestParams = $this->createMock(RequestParams::class);

        $algoApi = $this->createMock(AlgoApi::class);
        $algoApi->method('getId')->willReturn($algoApiId);

        $request = new NerEventsAlgoApiRequest($algoApi, $this->faker->url(), $requestParams);

        $this->assertSame($algoApiId, $request->getAlgoApiId());
    }

    /**
     * @throws Exception
     */
    public function testGetIndustryId(): void
    {
        $industryId = $this->faker->numberBetween(1, 100);

        $requestParams = $this->createMock(RequestParams::class);

        $algoApi = $this->createMock(AlgoApi::class);
        $algoApi->method('getIndustryId')->willReturn($industryId);

        $request = new NerEventsAlgoApiRequest($algoApi, $this->faker->url(), $requestParams);

        $this->assertSame($industryId, $request->getIndustryId());
    }

    /**
     * @throws Exception
     */
    public function testGetAlgoApiPath(): void
    {
        $algoApiPath = $this->faker->filePath();

        $requestParams = $this->createMock(RequestParams::class);

        $algoApi = $this->createMock(AlgoApi::class);
        $algoApi->method('getPath')->willReturn($algoApiPath);

        $request = new NerEventsAlgoApiRequest($algoApi, $this->faker->url(), $requestParams);

        $this->assertSame($algoApiPath, $request->getAlgoApiPath());
    }
}
