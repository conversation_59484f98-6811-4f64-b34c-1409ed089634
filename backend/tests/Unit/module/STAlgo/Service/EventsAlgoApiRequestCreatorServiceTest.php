<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service;

use PHPUnit\Framework\MockObject\Exception;
use ReflectionException;
use STAlgo\Service\AlgoEvents\RequestCreation\LlmEventsAlgoApiRequest;
use STAlgo\Service\AlgoEvents\RequestCreation\LlmEventsAlgoApiRequestCreator;
use STAlgo\Service\AlgoEvents\RequestCreation\NerEventsAlgoApiRequest;
use STAlgo\Service\AlgoEvents\RequestCreation\NerEventsAlgoApiRequestCreator;
use STAlgo\Service\EventsAlgoApiRequestCreatorService;
use STAlgo\Service\ParamsBuilding\RequestParamsBuilder;
use STAlgo\Service\ParamsBuilding\RequestParams;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Entity\Call;
use STCompany\Entity\Company;
use tests\TestCase;

class EventsAlgoApiRequestCreatorServiceTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ReflectionException
     * @throws NotFoundApiException
     */
    public function testCreate(): void
    {
        $call = $this->createMock(Call::class);
        $company = $this->createMock(Company::class);

        $requestParams = $this->createMock(RequestParams::class);

        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $requestParamsBuilder
            ->method('build')
            ->with($company, $call)
            ->willReturn($requestParams);

        $nerEventsAlgoApiRequest1 = $this->createMock(NerEventsAlgoApiRequest::class);
        $nerEventsAlgoApiRequest2 = $this->createMock(NerEventsAlgoApiRequest::class);

        $llmEventsAlgoApiRequest = $this->createMock(LlmEventsAlgoApiRequest::class);

        $nerEventsAlgoApiRequestCreator = $this->createMock(NerEventsAlgoApiRequestCreator::class);
        $nerEventsAlgoApiRequestCreator
            ->method('create')
            ->with($company, $requestParams)
            ->willReturn([$nerEventsAlgoApiRequest1, $nerEventsAlgoApiRequest2]);

        $llmEventsAlgoApiRequestCreator = $this->createMock(LlmEventsAlgoApiRequestCreator::class);
        $llmEventsAlgoApiRequestCreator
            ->method('create')
            ->with(
                $company,
                self::callback(
                    function (RequestParams $actualRequestParams) use ($requestParams) {
                        return $requestParams !== $actualRequestParams && $requestParams->toArray(
                            ) === $actualRequestParams->toArray();
                    }
                ),
            )
            ->willReturn($llmEventsAlgoApiRequest);

        $expectedRequests = [$nerEventsAlgoApiRequest1, $nerEventsAlgoApiRequest2, $llmEventsAlgoApiRequest];

        $creator = new EventsAlgoApiRequestCreatorService(
            $requestParamsBuilder,
            $nerEventsAlgoApiRequestCreator,
            $llmEventsAlgoApiRequestCreator
        );
        $requests = $creator->create($company, $call);

        $this->assertSame($expectedRequests, $requests);
    }

    public function testCreateWhenWithoutLlm(): void
    {
        $call = $this->createMock(Call::class);
        $company = $this->createMock(Company::class);

        $requestParams = $this->createMock(RequestParams::class);

        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $requestParamsBuilder
            ->method('build')
            ->with($company, $call)
            ->willReturn($requestParams);

        $nerEventsAlgoApiRequest = $this->createMock(NerEventsAlgoApiRequest::class);

        $llmEventsAlgoApiRequest = $this->createMock(LlmEventsAlgoApiRequest::class);

        $nerEventsAlgoApiRequestCreator = $this->createMock(NerEventsAlgoApiRequestCreator::class);
        $nerEventsAlgoApiRequestCreator
            ->method('create')
            ->with($company, $requestParams)
            ->willReturn([$nerEventsAlgoApiRequest]);

        $llmEventsAlgoApiRequestCreator = $this->createMock(LlmEventsAlgoApiRequestCreator::class);
        $llmEventsAlgoApiRequestCreator
            ->method('create')
            ->with($company, $requestParams)
            ->willReturn($llmEventsAlgoApiRequest);

        $expectedRequests = [$nerEventsAlgoApiRequest];

        $creator = new EventsAlgoApiRequestCreatorService(
            $requestParamsBuilder,
            $nerEventsAlgoApiRequestCreator,
            $llmEventsAlgoApiRequestCreator
        );
        $requests = $creator->create($company, $call, withoutLlm: true);

        $this->assertSame($expectedRequests, $requests);
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function testCreateWhenAlgoApiParams(): void
    {
        $algoApiParams = [['key1' => 'value1'], ['key2' => 'value2']];

        $call = $this->createMock(Call::class);
        $company = $this->createMock(Company::class);

        $requestParams = $this->createMock(RequestParams::class);

        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $requestParamsBuilder
            ->method('build')
            ->with($company, $call)
            ->willReturn($requestParams);

        $nerEventsAlgoApiRequest = $this->createMock(NerEventsAlgoApiRequest::class);
        $llmEventsAlgoApiRequest = $this->createMock(LlmEventsAlgoApiRequest::class);

        $nerEventsAlgoApiRequestCreator = $this->createMock(NerEventsAlgoApiRequestCreator::class);
        $nerEventsAlgoApiRequestCreator
            ->method('create')
            ->with($company, $requestParams, $algoApiParams)
            ->willReturn([$nerEventsAlgoApiRequest]);

        $llmEventsAlgoApiRequestCreator = $this->createMock(LlmEventsAlgoApiRequestCreator::class);
        $llmEventsAlgoApiRequestCreator
            ->method('create')
            ->with($company, $requestParams)
            ->willReturn($llmEventsAlgoApiRequest);

        $expectedRequests = [$nerEventsAlgoApiRequest, $llmEventsAlgoApiRequest];

        $creator = new EventsAlgoApiRequestCreatorService(
            $requestParamsBuilder,
            $nerEventsAlgoApiRequestCreator,
            $llmEventsAlgoApiRequestCreator
        );
        $requests = $creator->create($company, $call, $algoApiParams);

        $this->assertSame($expectedRequests, $requests);
    }

    /**
     * @return void
     * @throws Exception
     * @throws NotFoundApiException
     * @throws ReflectionException
     */
    public function testCreateWhenNoLlmRequest(): void
    {
        $call = $this->createMock(Call::class);
        $company = $this->createMock(Company::class);

        $requestParams = $this->createMock(RequestParams::class);

        $requestParamsBuilder = $this->createMock(RequestParamsBuilder::class);
        $requestParamsBuilder
            ->method('build')
            ->with($company, $call)
            ->willReturn($requestParams);

        $nerEventsAlgoApiRequest1 = $this->createMock(NerEventsAlgoApiRequest::class);
        $nerEventsAlgoApiRequest2 = $this->createMock(NerEventsAlgoApiRequest::class);

        $nerEventsAlgoApiRequestCreator = $this->createMock(NerEventsAlgoApiRequestCreator::class);
        $nerEventsAlgoApiRequestCreator
            ->method('create')
            ->with($company)
            ->willReturn([$nerEventsAlgoApiRequest1, $nerEventsAlgoApiRequest2]);

        $llmEventsAlgoApiRequestCreator = $this->createMock(LlmEventsAlgoApiRequestCreator::class);
        $llmEventsAlgoApiRequestCreator
            ->method('create')
            ->with($company)
            ->willReturn(null);

        $expectedRequests = [$nerEventsAlgoApiRequest1, $nerEventsAlgoApiRequest2];

        $creator = new EventsAlgoApiRequestCreatorService(
            $requestParamsBuilder,
            $nerEventsAlgoApiRequestCreator,
            $llmEventsAlgoApiRequestCreator
        );
        $requests = $creator->create($company, $call);

        $this->assertSame($expectedRequests, $requests);
    }
}
