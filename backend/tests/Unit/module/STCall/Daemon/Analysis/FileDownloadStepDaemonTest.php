<?php

namespace tests\Unit\module\STCall\Daemon\Analysis;

use PHPUnit\Framework\MockObject\Exception;
use RuntimeException;
use STApi\Entity\Exception\CallUpload\FileIsTooBigException;
use STA<PERSON>\Entity\Exception\CallUpload\IdenticalFileException;
use ST<PERSON><PERSON>\Entity\Exception\CallUpload\InvalidContentException;
use STApi\Entity\Exception\CallUpload\TooLowBalanceForAnalyzeException;
use STApi\Entity\Exception\InvalidCompanyApiRequestException;
use STCall\Daemon\Analysis\FileDownloadStepDaemon;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\CallAnalysis\Exception\StepIsFailedWithErrorFromTranscribingDriverException;
use STCall\Service\CallAnalysis\FileDownloadStep;
use STRoboTruck\Service\DataCollection\DataCollector;
use tests\TestCase;
use tests\WithConsecutive;

class FileDownloadStepDaemonTest extends TestCase
{
    /**
     * @throws InvalidContentException
     * @throws StepIsFailedWithErrorFromTranscribingDriverException
     * @throws IdenticalFileException
     * @throws InvalidCompanyApiRequestException
     * @throws Exception
     * @throws FileIsTooBigException
     * @throws StepIsAlreadyFinishedException
     * @throws TooLowBalanceForAnalyzeException
     */
    public function testHandle(): void
    {
        $roboTruckEventName = 'call_analyze_step_daemon';

        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->text(15);
        $data = [
            'company_id' => $companyId,
            'call_id' => $callId,
        ];

        $queueName = 'call-file-download-step';
        $step = $this->createMock(FileDownloadStep::class);
        $step
            ->method('applyOptions')
            ->willReturn($step);
        $step
            ->method('getQueueName')
            ->willReturn($queueName);

        $roboTruckEventStartMessage = $queueName . ' is started';
        $roboTruckEventEndMessage = 'call-file-download-step is finished in ';
        $roboTruckEventExtra = [
            'id' => null,
            'company_id' => $companyId,
            'call_id' => $callId,
        ];

        $oldLogName = 'api-calls-logs';
        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->exactly(2))
            ->method('collect')
            ->with(
                ...
                WithConsecutive::create(
                    [$roboTruckEventName, $roboTruckEventStartMessage, $roboTruckEventExtra, $oldLogName],
                    [
                        $roboTruckEventName,
                        self::callback(
                            function (string $message) use ($roboTruckEventEndMessage): bool {
                                return str_starts_with($message, $roboTruckEventEndMessage);
                            }
                        ),
                        $roboTruckEventExtra,
                        $oldLogName
                    ],
                )
            );

        $daemon = new FileDownloadStepDaemon($dataCollector);
        $params = $daemon->params();
        $params->add($step, 'step');

        $daemon->handle(json_encode($data));
    }

    /**
     * @throws StepIsFailedWithErrorFromTranscribingDriverException
     * @throws InvalidContentException
     * @throws IdenticalFileException
     * @throws InvalidCompanyApiRequestException
     * @throws Exception
     * @throws FileIsTooBigException
     * @throws StepIsAlreadyFinishedException
     * @throws TooLowBalanceForAnalyzeException
     */
    public function testHandleWhenSomeException(): void
    {
        $roboTruckEventName = 'call_analyze_step_daemon';

        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->text(15);
        $data = [
            'company_id' => $companyId,
            'call_id' => $callId,
        ];

        $queueName = 'call-file-download-step';
        $error = $this->faker->text(30);

        $step = $this->createMock(FileDownloadStep::class);
        $step
            ->method('getQueueName')
            ->willReturn($queueName);
        $step
            ->method('applyOptions')
            ->willThrowException(new RuntimeException($error));

        $roboTruckEventStartMessage = $queueName . ' is started';
        $roboTruckEventExtra = [
            'id' => null,
            'company_id' => $companyId,
            'call_id' => $callId,
        ];

        $oldLogName = 'api-calls-logs';
        $dataCollector = $this->createMock(DataCollector::class);
        $dataCollector
            ->expects($this->exactly(2))
            ->method('collect')
            ->with(
                ...
                WithConsecutive::create(
                    [$roboTruckEventName, $roboTruckEventStartMessage, $roboTruckEventExtra, $oldLogName],
                    [$roboTruckEventName, $error, $roboTruckEventExtra, $oldLogName],
                )
            );

        $daemon = new FileDownloadStepDaemon($dataCollector);
        $params = $daemon->params();
        $params->add($step, 'step');

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage($error);

        $daemon->handle(json_encode($data));
    }
}
