<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Daemon\Export;

use Carbon\Carbon;
use PHPUnit\Framework\MockObject\Exception;
use RuntimeException;
use STCall\Data\CallExportRepository;
use STCall\Service\Export\CallExportGenerator;
use STCall\Service\Export\ExternalFileService;
use STCall\Service\Export\FileNameGenerator;
use STCompany\Entity\Company;
use tests\TestCase;

final class CallExportGeneratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGenerateCalls(): void
    {
        $exportType = 'calls';

        $companyId = $this->faker->numberBetween(1, 100);

        $region = $this->faker->sentence(3);
        $exportBucketName = $this->faker->word();

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getAwsS3ExportBucketRegion')->willReturn($region);
        $company->method('getAwsS3ExportBucketName')->willReturn($exportBucketName);

        $startDate = Carbon::now()->subDay();
        $endDate = Carbon::now()->subSecond();

        $fileName = $this->faker->url();
        $fileNameGenerator = $this->createMock(FileNameGenerator::class);
        $fileNameGenerator
            ->method('generate')
            ->with($company, $exportType, $startDate, $endDate)
            ->willReturn($fileName);

        $exportData = $this->faker->text();

        $callExportRepository = $this->createMock(CallExportRepository::class);
        $callExportRepository
            ->method('getCallsCsvExport')
            ->with(
                $companyId,
                self::callback(
                    function (Carbon $actualStartDate) use ($startDate) {
                        return $actualStartDate->format('Y-m-d H:i:s') === $startDate->format('Y-m-d H:i:s');
                    }
                ),
                self::callback(
                    function (Carbon $actualEndDate) use ($endDate) {
                        return $actualEndDate->format('Y-m-d H:i:s') === $endDate->format('Y-m-d H:i:s');
                    }
                )
            )
            ->willReturn($exportData);

        $externalFileService = $this->createMock(ExternalFileService::class);
        $externalFileService
            ->expects($this->once())
            ->method('saveFileContent')
            ->with($fileName, $exportData, $exportBucketName, $region);

        $generator = new CallExportGenerator($callExportRepository, $fileNameGenerator, $externalFileService);
        $generator->generate($company, $startDate, $endDate, $exportType);
    }

    /**
     * @throws Exception
     */
    public function testGenerateCallsWhenData(): void
    {
        $exportType = 'calls';

        $companyId = $this->faker->numberBetween(1, 100);

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $startDate = Carbon::now()->subDay();
        $endDate = Carbon::now()->subSecond();

        $callExportRepository = $this->createMock(CallExportRepository::class);
        $callExportRepository
            ->method('getCallsCsvExport')
            ->with(
                $companyId,
                self::callback(
                    function (Carbon $actualStartDate) use ($startDate) {
                        return $actualStartDate->format('Y-m-d H:i:s') === $startDate->format('Y-m-d H:i:s');
                    }
                ),
                self::callback(
                    function (Carbon $actualEndDate) use ($endDate) {
                        return $actualEndDate->format('Y-m-d H:i:s') === $endDate->format('Y-m-d H:i:s');
                    }
                )
            )
            ->willReturn(null);

        $fileNameGenerator = $this->createMock(FileNameGenerator::class);

        $externalFileService = $this->createMock(ExternalFileService::class);
        $externalFileService
            ->expects($this->never())
            ->method('saveFileContent');

        $generator = new CallExportGenerator($callExportRepository, $fileNameGenerator, $externalFileService);
        $generator->generate($company, $startDate, $endDate, $exportType);
    }

    /**
     * @throws Exception
     */
    public function testGenerateParagraphs(): void
    {
        $exportType = 'paragraphs';

        $companyId = $this->faker->numberBetween(1, 100);

        $region = $this->faker->sentence(3);
        $exportBucketName = $this->faker->word();

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getAwsS3ExportBucketRegion')->willReturn($region);
        $company->method('getAwsS3ExportBucketName')->willReturn($exportBucketName);

        $startDate = Carbon::now()->subDay();
        $endDate = Carbon::now()->subSecond();

        $fileName = $this->faker->url();
        $fileNameGenerator = $this->createMock(FileNameGenerator::class);
        $fileNameGenerator
            ->method('generate')
            ->with($company, $exportType, $startDate, $endDate)
            ->willReturn($fileName);

        $exportData = $this->faker->text();

        $callExportRepository = $this->createMock(CallExportRepository::class);
        $callExportRepository
            ->method('getParagraphsCsvExport')
            ->with(
                $company,
                self::callback(
                    function (Carbon $actualStartDate) use ($startDate) {
                        return $actualStartDate->format('Y-m-d H:i:s') === $startDate->format('Y-m-d H:i:s');
                    }
                ),
                self::callback(
                    function (Carbon $actualEndDate) use ($endDate) {
                        return $actualEndDate->format('Y-m-d H:i:s') === $endDate->format('Y-m-d H:i:s');
                    }
                )
            )
            ->willReturn($exportData);

        $externalFileService = $this->createMock(ExternalFileService::class);
        $externalFileService
            ->expects($this->once())
            ->method('saveFileContent')
            ->with($fileName, $exportData, $exportBucketName, $region);

        $generator = new CallExportGenerator($callExportRepository, $fileNameGenerator, $externalFileService);
        $generator->generate($company, $startDate, $endDate, $exportType);
    }

    /**
     * @throws Exception
     */
    public function testGenerateParagraphsWhenNoData(): void
    {
        $exportType = 'paragraphs';

        $companyId = $this->faker->numberBetween(1, 100);

        $region = $this->faker->sentence(3);
        $exportBucketName = $this->faker->word();

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $startDate = Carbon::now()->subDay();
        $endDate = Carbon::now()->subSecond();

        $fileNameGenerator = $this->createMock(FileNameGenerator::class);

        $callExportRepository = $this->createMock(CallExportRepository::class);
        $callExportRepository
            ->method('getParagraphsCsvExport')
            ->with(
                $company,
                self::callback(
                    function (Carbon $actualStartDate) use ($startDate) {
                        return $actualStartDate->format('Y-m-d H:i:s') === $startDate->format('Y-m-d H:i:s');
                    }
                ),
                self::callback(
                    function (Carbon $actualEndDate) use ($endDate) {
                        return $actualEndDate->format('Y-m-d H:i:s') === $endDate->format('Y-m-d H:i:s');
                    }
                )
            )
            ->willReturn(null);

        $externalFileService = $this->createMock(ExternalFileService::class);
        $externalFileService
            ->expects($this->never())
            ->method('saveFileContent');

        $generator = new CallExportGenerator($callExportRepository, $fileNameGenerator, $externalFileService);
        $generator->generate($company, $startDate, $endDate, $exportType);
    }

    /**
     * @throws Exception
     */
    public function testGenerateEvents(): void
    {
        $exportType = 'events';

        $companyId = $this->faker->numberBetween(1, 100);

        $region = $this->faker->sentence(3);
        $exportBucketName = $this->faker->word();

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);
        $company->method('getAwsS3ExportBucketRegion')->willReturn($region);
        $company->method('getAwsS3ExportBucketName')->willReturn($exportBucketName);

        $startDate = Carbon::now()->subDay();
        $endDate = Carbon::now()->subSecond();

        $fileName = $this->faker->url();
        $fileNameGenerator = $this->createMock(FileNameGenerator::class);
        $fileNameGenerator
            ->method('generate')
            ->with($company, $exportType, $startDate, $endDate)
            ->willReturn($fileName);

        $exportData = $this->faker->text();

        $callExportRepository = $this->createMock(CallExportRepository::class);
        $callExportRepository
            ->method('getEventsCsvExport')
            ->with(
                $companyId,
                self::callback(
                    function (Carbon $actualStartDate) use ($startDate) {
                        return $actualStartDate->format('Y-m-d H:i:s') === $startDate->format('Y-m-d H:i:s');
                    }
                ),
                self::callback(
                    function (Carbon $actualEndDate) use ($endDate) {
                        return $actualEndDate->format('Y-m-d H:i:s') === $endDate->format('Y-m-d H:i:s');
                    }
                )
            )
            ->willReturn($exportData);

        $externalFileService = $this->createMock(ExternalFileService::class);
        $externalFileService
            ->expects($this->once())
            ->method('saveFileContent')
            ->with($fileName, $exportData, $exportBucketName, $region);

        $generator = new CallExportGenerator($callExportRepository, $fileNameGenerator, $externalFileService);
        $generator->generate($company, $startDate, $endDate, $exportType);
    }

    public function testGenerateEventsWhenNoData(): void
    {
        $exportType = 'events';

        $companyId = $this->faker->numberBetween(1, 100);

        $region = $this->faker->sentence(3);
        $exportBucketName = $this->faker->word();

        $company = $this->createMock(Company::class);
        $company->method('getId')->willReturn($companyId);

        $startDate = Carbon::now()->subDay();
        $endDate = Carbon::now()->subSecond();

        $fileNameGenerator = $this->createMock(FileNameGenerator::class);

        $callExportRepository = $this->createMock(CallExportRepository::class);
        $callExportRepository
            ->method('getEventsCsvExport')
            ->with(
                $companyId,
                self::callback(
                    function (Carbon $actualStartDate) use ($startDate) {
                        return $actualStartDate->format('Y-m-d H:i:s') === $startDate->format('Y-m-d H:i:s');
                    }
                ),
                self::callback(
                    function (Carbon $actualEndDate) use ($endDate) {
                        return $actualEndDate->format('Y-m-d H:i:s') === $endDate->format('Y-m-d H:i:s');
                    }
                )
            )
            ->willReturn(null);

        $externalFileService = $this->createMock(ExternalFileService::class);
        $externalFileService
            ->expects($this->never())
            ->method('saveFileContent');

        $generator = new CallExportGenerator($callExportRepository, $fileNameGenerator, $externalFileService);
        $generator->generate($company, $startDate, $endDate, $exportType);
    }

    /**
     * @throws Exception
     */
    public function testGenerateWhenUnknownExportType(): void
    {
        $exportType = $this->faker->sentence(3);

        $company = $this->createMock(Company::class);

        $startDate = Carbon::now()->subDay();
        $endDate = Carbon::now()->subSecond();

        $fileNameGenerator = $this->createMock(FileNameGenerator::class);
        $callExportRepository = $this->createMock(CallExportRepository::class);
        $externalFileService = $this->createMock(ExternalFileService::class);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Unknown export type: ' . $exportType);

        $generator = new CallExportGenerator($callExportRepository, $fileNameGenerator, $externalFileService);
        $generator->generate($company, $startDate, $endDate, $exportType);
    }
}
