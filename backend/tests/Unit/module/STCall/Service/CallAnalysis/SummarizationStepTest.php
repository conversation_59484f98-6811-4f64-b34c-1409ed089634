<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\CallAnalysis;

use PHPUnit\Framework\MockObject\Exception;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsTable;
use ST<PERSON>all\Entity\Call;
use ST<PERSON>all\Entity\CallFactory;
use ST<PERSON>all\Entity\CallSummarization;
use STCall\Service\CallAnalysis\CallSelection\CallSelector;
use STCall\Service\CallAnalysis\Exception\StepIsAlreadyFinishedException;
use STCall\Service\CallAnalysis\Summarization\CallSummarizationSaver;
use STCall\Service\CallAnalysis\Summarization\Interfaces\CallSummarizationGetterInterface;
use STCall\Service\CallAnalysis\SummarizationStep;
use STCall\Service\EventTriggerService;
use STCall\Service\Interfaces\CompanySelectorInterface;
use STCompany\Entity\Company;
use tests\TestCase;

final class SummarizationStepTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetQueueNames(): void
    {
        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $callSelector = $this->createMock(CallSelector::class);
        $companySelector = $this->createMock(CompanySelectorInterface::class);
        $callSummarizationGetter = $this->createMock(CallSummarizationGetterInterface::class);
        $callSummarizationSaver = $this->createMock(CallSummarizationSaver::class);
        $eventTriggerService = $this->createMock(EventTriggerService::class);

        $step = new SummarizationStep(
            $callsTable,
            $callFactory,
            $callSelector,
            $companySelector,
            $callSummarizationGetter,
            $callSummarizationSaver,
            $eventTriggerService
        );

        $this->assertSame('call-summarization-step', $step->getQueueName());
        $this->assertSame('call-summarization-step-error', $step->getErrorQueueName());
        $this->assertSame('call-llm-events-detection-step', $step->getNextStepQueue());
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testLaunch(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();

        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);

        $isSummarizationCompletedMap = [
            [null, false],
            [true, $call]
        ];
        $call->method('isSummarizationCompleted')->willReturn(false);
        $call
            ->expects($this->exactly(2))
            ->method('isSummarizationCompleted')
            ->willReturnMap($isSummarizationCompletedMap);

        $callSelector = $this->createMock(CallSelector::class);
        $callSelector->method('getCall')->with($callId, $companyId)->willReturn($call);

        $callsTable = $this->createMock(CallsTable::class);
        $callsTable->expects($this->once())->method('saveCall')->with($call);

        $callFactory = $this->createMock(CallFactory::class);

        $company = $this->createMock(Company::class);
        $company->method('isSummarizationEnabled')->willReturn(true);
        $company->method('getId')->willReturn($companyId);

        $companySelector = $this->createMock(CompanySelectorInterface::class);
        $companySelector->method('getCompany')->with($companyId)->willReturn($company);

        $summarizationData = [
            $this->faker->word() => $this->faker->word(),
            $this->faker->word() => $this->faker->word()
        ];
        $callSummarization = $this->createMock(CallSummarization::class);
        $callSummarization->method('toArray')->willReturn($summarizationData);

        $callSummarizationGetter = $this->createMock(CallSummarizationGetterInterface::class);
        $callSummarizationGetter
            ->method('getSummarization')
            ->with($company, $call)
            ->willReturn($callSummarization);

        $callSummarizationSaver = $this->createMock(CallSummarizationSaver::class);
        $callSummarizationSaver
            ->expects($this->once())
            ->method('saveCallSummarization')
            ->with($callSummarization);

        $eventName = 'call-summarization-step-finished';
        $eventParams = [
            'queue_name' => 'call-summarization-step',
            'company_id' => $companyId,
            'call_id' => $callId,
            'data' => $summarizationData
        ];

        $eventTriggerService = $this->createMock(EventTriggerService::class);
        $eventTriggerService
            ->expects($this->once())
            ->method('trigger')
            ->with($eventName, $eventParams);

        $step = new SummarizationStep(
            $callsTable,
            $callFactory,
            $callSelector,
            $companySelector,
            $callSummarizationGetter,
            $callSummarizationSaver,
            $eventTriggerService
        );
        $this->assertTrue($step->launch($callId, $companyId));
    }

    /**
     * @throws Exception
     * @throws NotFoundApiException
     */
    public function testLaunchWhenStepAlreadyCompleted(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();

        $call = $this->createMock(Call::class);
        $call->method('isSummarizationCompleted')->willReturn(true);

        $callSelector = $this->createMock(CallSelector::class);
        $callSelector->method('getCall')->with($callId, $companyId)->willReturn($call);

        $callsTable = $this->createMock(CallsTable::class);
        $callFactory = $this->createMock(CallFactory::class);
        $companySelector = $this->createMock(CompanySelectorInterface::class);
        $callSummarizationGetter = $this->createMock(CallSummarizationGetterInterface::class);
        $callSummarizationSaver = $this->createMock(CallSummarizationSaver::class);
        $eventTriggerService = $this->createMock(EventTriggerService::class);

        $this->expectException(StepIsAlreadyFinishedException::class);
        $this->expectExceptionMessage('Summarization step is finished');

        $step = new SummarizationStep(
            $callsTable,
            $callFactory,
            $callSelector,
            $companySelector,
            $callSummarizationGetter,
            $callSummarizationSaver,
            $eventTriggerService
        );
        $step->launch($callId, $companyId);
    }

    /**
     * @throws NotFoundApiException
     * @throws Exception
     */
    public function testLaunchWhenSummarizationIsNotEnabledForCompany(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->uuid();

        $call = $this->createMock(Call::class);

        $isSummarizationCompletedMap = [
            [null, false],
            [true, $call]
        ];
        $call->method('isSummarizationCompleted')->willReturn(false);
        $call
            ->expects($this->exactly(2))
            ->method('isSummarizationCompleted')
            ->willReturnMap($isSummarizationCompletedMap);

        $callSelector = $this->createMock(CallSelector::class);
        $callSelector->method('getCall')->with($callId, $companyId)->willReturn($call);

        $callsTable = $this->createMock(CallsTable::class);
        $callsTable->expects($this->once())->method('saveCall')->with($call);

        $callFactory = $this->createMock(CallFactory::class);

        $company = $this->createMock(Company::class);
        $company->method('isSummarizationEnabled')->willReturn(false);

        $companySelector = $this->createMock(CompanySelectorInterface::class);
        $companySelector->method('getCompany')->with($companyId)->willReturn($company);

        $callSummarizationGetter = $this->createMock(CallSummarizationGetterInterface::class);
        $callSummarizationSaver = $this->createMock(CallSummarizationSaver::class);
        $eventTriggerService = $this->createMock(EventTriggerService::class);

        $step = new SummarizationStep(
            $callsTable,
            $callFactory,
            $callSelector,
            $companySelector,
            $callSummarizationGetter,
            $callSummarizationSaver,
            $eventTriggerService
        );
        $this->assertTrue($step->launch($callId, $companyId));
    }
}
