<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\Webhooks;

use GuzzleHttp\Exception\GuzzleException;
use Laminas\Http\Client;
use Laminas\Http\Response;
use PHPUnit\Framework\MockObject\Exception;
use ST<PERSON>pi\Entity\Exception\ThirdPartyApiException;
use STCall\Service\Webhooks\WebhookSender;
use tests\TestCase;

final class WebhookSenderTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ThirdPartyApiException
     */
    public function testSendWhen(): void
    {
        $url = $this->faker->url();
        $data = [
            [$this->faker->word() => $this->faker->word(), $this->faker->word() => $this->faker->word()],
            [$this->faker->word() => $this->faker->word(), $this->faker->sentence() => $this->faker->word()],
        ];

        $headers = [$url, $this->faker->word(), $this->faker->word()];
        $response = $this->createMock(Response::class);
        $response->method('isSuccess')->willReturn(true);

        $clientOptions = ['timeout' => 3, 'connect_timeout' => 1];
        $client = $this->createMock(Client::class);
        $client->expects($this->once())->method('setUri')->with($url);
        $client->expects($this->once())->method('setHeaders')->with($headers);
        $client->expects($this->once())->method('setMethod')->with('POST');
        $client->expects($this->once())->method('setRawBody')->with(json_encode($data));
        $client->expects($this->once())->method('setOptions')->with($clientOptions);
        $client->method('send')->willReturn($response);

        $sender = $this
            ->getMockBuilder(WebhookSender::class)
            ->onlyMethods(['createClient'])
            ->getMock();
        $sender->method('createClient')->willReturn($client);

        $sender->send($url, $data, $headers);
    }

    public function testSendWhenNoHeaders(): void
    {
        $url = $this->faker->url();
        $data = [
            [$this->faker->word() => $this->faker->word(), $this->faker->word() => $this->faker->word()],
            [$this->faker->word() => $this->faker->word(), $this->faker->sentence() => $this->faker->word()],
        ];

        $headers = null;
        $defaultHeaders = [
            'Content-type' => 'application/json'
        ];
        $response = $this->createMock(Response::class);
        $response->method('isSuccess')->willReturn(true);

        $clientOptions = ['timeout' => 3, 'connect_timeout' => 1];
        $client = $this->createMock(Client::class);
        $client->expects($this->once())->method('setUri')->with($url);
        $client->expects($this->once())->method('setHeaders')->with($defaultHeaders);
        $client->expects($this->once())->method('setMethod')->with('POST');
        $client->expects($this->once())->method('setRawBody')->with(json_encode($data));
        $client->expects($this->once())->method('setOptions')->with($clientOptions);
        $client->method('send')->willReturn($response);

        $sender = $this
            ->getMockBuilder(WebhookSender::class)
            ->onlyMethods(['createClient'])
            ->getMock();
        $sender->method('createClient')->willReturn($client);

        $sender->send($url, $data, $headers);
    }

    /**
     * @throws GuzzleException
     * @throws Exception
     */
    public function testSendWhenUnsuccessfulResponse(): void
    {
        $url = $this->faker->url();
        $data = ['data'];

        $statusCode = $this->faker->numberBetween(300, 600);
        $responseBody = $this->faker->text();

        $response = $this->createMock(Response::class);
        $response->method('isSuccess')->willReturn(false);
        $response->method('getStatusCode')->willReturn($statusCode);
        $response->method('getBody')->willReturn($responseBody);

        $client = $this->createMock(Client::class);
        $client->method('send')->willReturn($response);

        $sender = $this
            ->getMockBuilder(WebhookSender::class)
            ->onlyMethods(['createClient'])
            ->getMock();
        $sender->method('createClient')->willReturn($client);

        $this->expectException(ThirdPartyApiException::class);
        $this->expectExceptionMessage(
            sprintf('Failed to send webhook: response code: %d, response body: %s', $statusCode, $responseBody)
        );

        $sender->send($url, $data);
    }
}
