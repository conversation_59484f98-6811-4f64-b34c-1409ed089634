<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service;

use Carbon\Carbon;
use PHPUnit\Framework\MockObject\Exception;
use STCall\Data\CallsChecklistsPointsTable;
use STCall\Service\CallChecklistService;
use STCall\Service\Interfaces\UserTeamIdsSelectorInterface;
use tests\TestCase;

class CallChecklistServiceTest extends TestCase
{
    /**
     * @throws Exception
     */
    public function testGetAgentsTotalStatistics(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $userId = $this->faker->numberBetween(101, 200);
        $checklistId = $this->faker->numberBetween(201, 300);
        $agentId = $this->faker->numberBetween(301, 400);
        $startDate = Carbon::parse('2023-01-01');
        $endDate = Carbon::parse('2023-01-31');

        $teamIds = [$this->faker->numberBetween(1, 10), $this->faker->numberBetween(11, 20)];

        $userTeamIdsSelector = $this->createMock(UserTeamIdsSelectorInterface::class);
        $userTeamIdsSelector
            ->method('getUserTeamIds')
            ->willReturn($teamIds);

        $agentsCallsSummary = [
            $agentId => [
                'calls_duration' => 3600,
                'calls_count' => 10,
                'clients_count' => 5,
                'agents_count' => 1
            ]
        ];

        $agentsTotalStatistics = [
            $agentId => [
                1 => [
                    'agent_id' => $agentId,
                    'agent_name' => 'John Doe',
                    'checklist_point_id' => 1,
                    'checklist_point_name' => 'Greeting',
                    'passed_calls_count' => 8,
                    'passed_percentage' => 80
                ],
                2 => [
                    'agent_id' => $agentId,
                    'agent_name' => 'John Doe',
                    'checklist_point_id' => 2,
                    'checklist_point_name' => 'Product Knowledge',
                    'passed_calls_count' => 7,
                    'passed_percentage' => 70
                ]
            ]
        ];

        $callsChecklistsPointsTable = $this->createMock(CallsChecklistsPointsTable::class);
        $callsChecklistsPointsTable
            ->method('getAgentsCallsSummary')
            ->with($companyId, $teamIds, $checklistId, $agentId, $startDate, $endDate)
            ->willReturn($agentsCallsSummary);
        $callsChecklistsPointsTable
            ->method('getAgentsTotalStatistics')
            ->with($companyId, $teamIds, $checklistId, $agentId, $startDate, $endDate)
            ->willReturn($agentsTotalStatistics);

        $callChecklistService = new CallChecklistService($userTeamIdsSelector, $callsChecklistsPointsTable);

        $result = $callChecklistService->getAgentsTotalStatistics(
            $companyId,
            $userId,
            $checklistId,
            $agentId,
            $startDate,
            $endDate
        );

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertEquals($agentId, $result[0]['agent_id']);
        $this->assertEquals('John Doe', $result[0]['agent_name']);
        $this->assertEquals(3600, $result[0]['calls_duration']);
        $this->assertEquals(10, $result[0]['total_conversations']);
        $this->assertEquals(5, $result[0]['clients_count']);
        $this->assertEquals(1, $result[0]['agents_count']);
        $this->assertEquals(75, $result[0]['score']); // (80 + 70) / 2
        $this->assertCount(2, $result[0]['checklist_points']);
    }

    /**
     * @throws Exception
     */
    public function testGetAgentsTotalStatisticsWithEmptyData(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $userId = $this->faker->numberBetween(101, 200);

        $agentsCallsSummary = [];
        $agentsTotalStatistics = [];

        $teamIds = [$this->faker->numberBetween(1, 10), $this->faker->numberBetween(11, 20)];

        $userTeamIdsSelector = $this->createMock(UserTeamIdsSelectorInterface::class);
        $userTeamIdsSelector
            ->method('getUserTeamIds')
            ->willReturn($teamIds);

        $callsChecklistsPointsTable = $this->createMock(CallsChecklistsPointsTable::class);
        $callsChecklistsPointsTable
            ->method('getAgentsCallsSummary')
            ->with($companyId, $teamIds, null, null, null, null)
            ->willReturn($agentsCallsSummary);

        $callsChecklistsPointsTable->expects($this->once())
            ->method('getAgentsTotalStatistics')
            ->with($companyId, $teamIds, null, null, null, null)
            ->willReturn($agentsTotalStatistics);

        $callChecklistService = new CallChecklistService($userTeamIdsSelector, $callsChecklistsPointsTable);

        $this->assertEquals(
            [],
            $callChecklistService->getAgentsTotalStatistics($companyId, $userId)
        );
    }
}
