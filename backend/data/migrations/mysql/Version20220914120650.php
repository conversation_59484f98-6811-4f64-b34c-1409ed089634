<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220914120650 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void {
        $table = $schema->createTable('api_applications');
        $table->addColumn('application_id', 'integer', [
            'notnull' => true,
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('application_name', 'string', [
            'notnull' => true,
            'length' => 31,
        ]);
        $table->addColumn('application_type', 'boolean', [
            'notnull' => true,
            'default' => 0,
            'unsigned' => true,
        ]);
        $table->addColumn('application_token', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);
        $table->setPrimaryKey([
            'application_id',
        ]);

        $table->addIndex([
            'application_token',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void {
        $schema->dropTable('api_applications');
    }

}
