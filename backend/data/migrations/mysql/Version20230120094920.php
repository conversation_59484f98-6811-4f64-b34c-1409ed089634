<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230120094920 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->dropColumn('call_template');
        $table->dropColumn('agent_call_template');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            ALTER TABLE
                companies
            ADD COLUMN
                call_template VARCHAR(255) DEFAULT NULL
            AFTER
                threshold_bar;
        ');
        $this->connection->executeQuery('
            ALTER TABLE
                companies
            ADD COLUMN
                agent_call_template VARCHAR(255) DEFAULT NULL
            AFTER
                call_template;
        ');
    }

}
