<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221118111342 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('events_colors');
        $table->addColumn('color_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('name', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);
        $table->addColumn('hex', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);
        $table->addColumn('priority', 'smallint', [
            'notnull' => true,
        ]);
        
        $table->setPrimaryKey([
            'color_id',
        ]);
        
        $table->addUniqueIndex([
            'priority',
        ]);
    }
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function postUp(Schema $schema): void {
        $this->connection->executeQuery('
            INSERT INTO
                events_colors
            VALUES
                (1, "red", "#FF8989", 1),
                (2, "yellow", "#CCCD70", 2),
                (3, "green", "#6DB976", 3),
                (4, "blue", "#7CCCFA", 4),
                (5, "orange", "#FFC35F", 5),
                (6, "soft orange", "#DEC3A8", 6),
                (7, "brown", "#DEC3A8", 7),
                (8, "grey", "#EEEEEE", 8)
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('events_colors');
    }

}
