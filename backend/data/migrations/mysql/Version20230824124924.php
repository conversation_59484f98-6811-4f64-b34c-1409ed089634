<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230824124924 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        // to ignore unique key
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = priority + 100
        ');
        
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = 1
            WHERE
                color_id = 1
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = 5
            WHERE
                color_id = 2
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = 4
            WHERE
                color_id = 3
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = 3
            WHERE
                color_id = 4
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = 6
            WHERE
                color_id = 5
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = 2
            WHERE
                color_id = 6
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = 7
            WHERE
                color_id = 7
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = 11
            WHERE
                color_id = 8
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = 8
            WHERE
                color_id = 9
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = 9
            WHERE
                color_id = 10
        ');
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = 10
            WHERE
                color_id = 11
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        // to ignore unique key
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = priority + 100
        ');
        
        $this->connection->executeQuery('
            UPDATE
                events_colors
            SET
                priority = color_id
        ');
    }

}
