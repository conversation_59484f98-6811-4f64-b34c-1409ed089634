<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230223133350 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            UPDATE
                algo_apis
            SET
                algo_api_category_id = 3
            WHERE
                path = "http://213.149.176.142:5000/questions/segmentation"
        ');
        $this->connection->executeQuery('
            INSERT INTO
                algo_apis
                (path, algo_api_category_id)
            VALUES
                ("http://213.149.176.142:5000/violations/segmentation", 1)
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            UPDATE
                algo_apis
            SET
                algo_api_category_id = 1
            WHERE
                path = "http://213.149.176.142:5000/questions/segmentation"
        ');
        $this->connection->executeQuery('
            DELETE FROM
                algo_apis
            WHERE
                path = "http://213.149.176.142:5000/violations/segmentation";
        ');
    }

}
