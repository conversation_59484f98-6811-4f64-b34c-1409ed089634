<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241122165625 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            INSERT INTO
                algo_apis_categories
                (algo_api_category_id, algo_api_category_name)
            VALUES
                (33, "LLM")
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery('
            DELETE
            FROM
                algo_apis_categories
            WHERE
                algo_api_category_id = 33
        ');
    }
}
