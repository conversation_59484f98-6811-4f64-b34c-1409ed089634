<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250310134258 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $result = $this->connection->executeQuery('
            SELECT
                company_id
            FROM 
                companies
        ');
        foreach ($result->fetchFirstColumn() as $companyId) {
            $sql = '
                DELETE
                FROM
                    users_teams
                WHERE
                    user_id IN (
                        SELECT
                            ucr.user_id
                        FROM
                            users_companies_roles ucr
                        INNER JOIN
                            roles r
                            ON ucr.role_id = r.role_id
                        WHERE
                            r.role_type = 3
                            AND r.company_id = ' . $companyId . '
                        ORDER BY
                            user_id
                    )
                    AND (user_id, team_id) NOT IN (
                        SELECT
                            ut.user_id,
                            MIN(ut.team_id)
                        FROM
                            users_teams ut
                        INNER JOIN teams t
                            ON ut.team_id = t.team_id
                        WHERE
                            t.company_id = ' . $companyId . '
                        GROUP BY
                            user_id
                    )
            ';
            $this->connection->executeQuery($sql);
        }
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {

    }

}
