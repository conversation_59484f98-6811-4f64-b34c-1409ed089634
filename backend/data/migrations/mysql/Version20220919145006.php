<?php

declare(strict_types=1);

namespace Migrations;

use <PERSON>trine\DBAL\Schema\Schema;
use <PERSON>trine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220919145006 extends AbstractMigration
{
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void {
        $table = $schema->createTable('features');
        $table->addColumn('feature_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('feature_name', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);
        $table->addColumn('parent_feature_id', 'smallint', [
            'notnull' => false,
            'unsigned' => true,
            'default' => null,
        ]);

        $table->setPrimaryKey([
            'feature_id',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function postUp(Schema $schema): void {
        $this->connection->executeQuery('
            INSERT INTO
                features
            VALUES
                (1, "Dashboard", null),
                (2, "Call statistics", null),
                (3, "Severe violations", null),
                (4, "Clients", null),
                (5, "Manager statistics", null),
                (6, "Users", null),
                (7, "Payments", null),
                (8, "Iconery", null),
                (9, "Calls overview", 1),
                (10, "Flags", 1),
                (11, "Agents", 1),
                (12, "Clients", 1),
                (13, "Group name", 2),
                (14, "Flag statistics", 2),
                (15, "Time on phone", 2)
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void {
        $schema->dropTable('features');
    }
    
}
