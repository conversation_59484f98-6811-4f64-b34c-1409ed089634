<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221104101340 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('algo_apis_categories');
        $table->addColumn('algo_api_category_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
        ]);

        $table->addColumn('algo_api_category_name', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);
        
        $table->addUniqueIndex([
            'algo_api_category_name',
        ]);

        $table->setPrimaryKey([
            'algo_api_category_id',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function postUp(Schema $schema): void {
        $this->connection->executeQuery('
            INSERT INTO
                algo_apis_categories
                (algo_api_category_id, algo_api_category_name)
            VALUES
                (1, "QC"),
                (2, "Sales"),
                (3, "Marketing"),
                (4, "Retention")
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('algo_apis_categories');
    }

}
