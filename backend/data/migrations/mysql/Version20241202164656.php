<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241202164656 extends AbstractMigration
{

    /**
     *
     * @param Schema $schema
     * @return void
     * @throws Exception
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery(
            '
            RENAME TABLE
                algo_apis_categories
            TO
                industries
        '
        );

        $table = $schema->getTable('industries');
        $table->renameColumn('algo_api_category_id', 'id');
        $table->renameColumn('algo_api_category_name', 'name');

        $table = $schema->getTable('algo_apis');
        $table->renameColumn('algo_api_category_id', 'industry_id');
    }

    /**
     *
     * @param Schema $schema
     * @return void
     * @throws Exception
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery(
            '
            RENAME TABLE
                industries
            TO
                algo_apis_categories
        '
        );

        $table = $schema->getTable('algo_apis_categories');
        $table->renameColumn('id', 'algo_api_category_id');
        $table->renameColumn('name', 'algo_api_category_name');

        $table = $schema->getTable('algo_apis');
        $table->renameColumn('industry_id', 'algo_api_category_id');
    }
}
