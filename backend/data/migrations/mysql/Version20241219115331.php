<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241219115331 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('industries_llm_events');

        $table->addColumn('industry_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addForeignKeyConstraint('industries', ['industry_id'], ['industry_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addColumn('llm_event_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addForeignKeyConstraint('llm_events', ['llm_event_id'], ['llm_event_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->setPrimaryKey(['industry_id', 'llm_event_id']);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('industries_llm_events');
    }
}
