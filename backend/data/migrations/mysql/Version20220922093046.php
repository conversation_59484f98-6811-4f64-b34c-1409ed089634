<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220922093046 extends AbstractMigration
{
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void {
        $table = $schema->createTable('permissions');
        $table->addColumn('permission_id', 'smallint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('parent_permission_id', 'smallint', [
            'notnull' => false,
            'unsigned' => true,
            'default' => null,
        ]);
        $table->addColumn('permission_name', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);
        
        $table->addForeignKeyConstraint('permissions', ['parent_permission_id'], ['permission_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->setPrimaryKey([
            'permission_id',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function postUp(Schema $schema): void {
        $this->connection->executeQuery('
            INSERT INTO
                permissions
                (permission_id, parent_permission_id, permission_name)
            VALUES
                (1, null, "Company"),
                (2, null, "Dashboard"),
                (3, null, "Call statistics"),
                (4, null, "Severe violations"),
                (5, null, "Clients"),
                (6, null, "Manager statistics"),
                (7, null, "Users"),
                (8, null, "Iconery"),
                (9, 1, "Payments"),
                (10, 1, "Profile"),
                (11, 1, "Users"),
                (12, 1, "Teams"),
                (13, 2, "Calls overview"),
                (14, 2, "Flags"),
                (15, 2, "Agents"),
                (16, 2, "Clients"),
                (17, 3, "Group name"),
                (18, 3, "Flag statistics"),
                (19, 3, "Time on phone")
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void {
        $schema->dropTable('permissions');
    }
    
}
