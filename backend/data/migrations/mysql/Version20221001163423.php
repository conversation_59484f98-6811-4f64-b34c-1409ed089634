<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221001163423 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('users_companies_roles');
        $table->dropPrimaryKey();
        $table->setPrimaryKey([
            'company_id',
            'user_id',
        ]);
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('users_companies_roles');
        $table->dropPrimaryKey();
        $table->setPrimaryKey([
            'company_id',
            'user_id',
            'role_id',
        ]);
    }

}
