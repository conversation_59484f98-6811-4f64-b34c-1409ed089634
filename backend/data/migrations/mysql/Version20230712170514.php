<?php

declare(strict_types=1);

namespace Migrations;

use <PERSON>trine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230712170514 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $this->connection->executeQuery('
            DELETE
            FROM
                permissions
            WHERE
                permission_id NOT IN (1,3,5,7,8,20,21,22)
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $this->connection->executeQuery("
            INSERT INTO
                permissions
                (permission_id,parent_permission_id,permission_name,system_name)
            VALUES
                (2,NULL,'Dashboard','dashboard'),
                (4,NULL,'Severe violations','severe_violations'),
                (6,NULL,'Manager statistics','manager_statistics'),
                (9,1,'Payments','payments'),
                (10,1,'Profile','profile'),
                (11,1,'Users','company_users'),
                (12,1,'Teams','teams'),
                (13,2,'Calls overview','calls_overview'),
                (14,2,'Flags','flags'),
                (15,2,'Agents','agents'),
                (16,2,'Clients','clients_dashboard'),
                (17,3,'Group name','group_name'),
                (18,3,'Flag statistics','flag_statistics'),
                (19,3,'Time on phone','time_on_phone')
        ");
    }

}
