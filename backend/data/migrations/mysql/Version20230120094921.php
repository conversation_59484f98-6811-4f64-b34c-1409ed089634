<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230120094921 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void {
        $table = $schema->createTable('companies_call_templates');
        
        $table->addColumn('call_template_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
            'autoincrement' => true,
        ]);
        $table->addColumn('company_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('regex', 'string', [
            'notnull' => true,
            'length' => 2047,
        ]);
        $table->addColumn('call_id_number', 'smallint', [
            'notnull' => false,
        ]);
        $table->addColumn('client_number', 'smallint', [
            'notnull' => true,
        ]);
        $table->addColumn('agent_number', 'smallint', [
            'notnull' => true,
        ]);
        $table->addColumn('date_number', 'smallint', [
            'notnull' => true,
        ]);
        $table->addColumn('time_number', 'smallint', [
            'notnull' => false,
        ]);
        
        
        $table->setPrimaryKey([
            'call_template_id',
        ]);

        $table->addForeignKeyConstraint('companies', ['company_id'], ['company_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void {
        $schema->dropTable('companies_call_templates');
    }

}
