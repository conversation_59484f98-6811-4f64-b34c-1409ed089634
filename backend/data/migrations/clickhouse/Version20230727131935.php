<?php

namespace Clickhouse\Migrations;

class Version20230727131935 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE api_calls_logs
        ');
        $this->getClient()->write('
            CREATE TABLE api_calls_logs (
                id String,
                company_id UInt32,
                message String,
                created DateTime DEFAULT now()
            )
            ENGINE = Log
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE api_calls_logs
        ');
        $this->getClient()->write('
            CREATE TABLE api_calls_logs (
                id String,
                message String,
                created DateTime DEFAULT now()
            )
            ENGINE = Log
        ');
    }
}
