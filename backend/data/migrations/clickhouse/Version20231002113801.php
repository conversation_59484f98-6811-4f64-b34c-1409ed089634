<?php

namespace Clickhouse\Migrations;

class Version20231002113801 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            RENAME TABLE
                api_calls_logs
            TO
                api_calls_logs_deprecated
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            RENAME TABLE
                api_calls_logs_deprecated
            TO
                api_calls_logs
        ');
    }
}
