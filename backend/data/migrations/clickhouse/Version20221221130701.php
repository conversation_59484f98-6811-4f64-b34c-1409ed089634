<?php

namespace Clickhouse\Migrations;

class Version20221221130701 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                transcribing_driver Nullable(String) DEFAULT NULL
            AFTER s3_file_path
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE 
                calls
            DROP COLUMN
                transcribing_driver
        ');
    }
}
