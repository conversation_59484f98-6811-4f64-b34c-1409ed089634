<?php

namespace Clickhouse\Migrations;

use STClickhouse\Entity\Migration\BaseMigration;

class Version20250218091722 extends BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE calls_summarizations (
                call_id String,
                company_id UInt32,
                overview String DEFAULT \'\',          
                details String DEFAULT \'\',          
                created_at DateTime DEFAULT now()
            )
            ENGINE = MergeTree()
            PARTITION BY toYYYYMM(created_at)
            ORDER BY (call_id, company_id)
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE calls_summarizations
        ');
    }
}
