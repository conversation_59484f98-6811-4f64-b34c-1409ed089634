<?php

namespace Clickhouse\Migrations;

class Version20240308073330 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_reviewed_clients_1
            DROP COLUMN IF EXISTS
                calls_count
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_reviewed_clients_2
            DROP COLUMN IF EXISTS
                calls_count
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_1
            ADD COLUMN IF NOT EXISTS
                commented_calls_count UInt32
            AFTER calls_count
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_2
            ADD COLUMN IF NOT EXISTS
                commented_calls_count UInt32
            AFTER calls_count
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            ADD COLUMN IF NOT EXISTS
                event_id UInt64
            AFTER team_ids
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            ADD COLUMN IF NOT EXISTS
                event_id UInt64
            AFTER team_ids
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            ADD COLUMN IF NOT EXISTS
                category_id UInt64
            AFTER team_ids
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            ADD COLUMN IF NOT EXISTS
                category_id UInt64
            AFTER team_ids
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            DROP COLUMN IF EXISTS
                fill_color_hex
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            DROP COLUMN IF EXISTS
                fill_color_hex
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            DROP COLUMN IF EXISTS
                outline_color_hex
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            DROP COLUMN IF EXISTS
                outline_color_hex
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            DROP COLUMN IF EXISTS
                event_name
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            DROP COLUMN IF EXISTS
                event_name
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            DROP COLUMN IF EXISTS
                category_name
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            DROP COLUMN IF EXISTS
                category_name
        ');

        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_calls_1
            DROP COLUMN IF EXISTS
                opened_duration
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_calls_2
            DROP COLUMN IF EXISTS
                opened_duration
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_calls_1
            DROP COLUMN IF EXISTS
                opened_count
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_calls_2
            DROP COLUMN IF EXISTS
                opened_count
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_reviewed_clients_1
            ADD COLUMN IF NOT EXISTS
                calls_count UInt32
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_reviewed_clients_2
            ADD COLUMN IF NOT EXISTS
                calls_count UInt32
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_1
            DROP COLUMN IF EXISTS
                commented_calls_count
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_managers_kpi_2
            DROP COLUMN IF EXISTS
                commented_calls_count
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            DROP COLUMN IF EXISTS
                event_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            DROP COLUMN IF EXISTS
                event_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            DROP COLUMN IF EXISTS
                category_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            DROP COLUMN IF EXISTS
                category_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            ADD COLUMN IF NOT EXISTS
                fill_color_hex String
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            ADD COLUMN IF NOT EXISTS
                fill_color_hex String
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            ADD COLUMN IF NOT EXISTS
                outline_color_hex String
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            ADD COLUMN IF NOT EXISTS
                outline_color_hex String
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            ADD COLUMN IF NOT EXISTS
                event_name String
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            ADD COLUMN IF NOT EXISTS
                event_name String
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_1
            ADD COLUMN IF NOT EXISTS
                category_name String
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_events_2
            ADD COLUMN IF NOT EXISTS
                category_name String
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_calls_1
            ADD COLUMN IF NOT EXISTS
                opened_duration UInt64
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_calls_2
            ADD COLUMN IF NOT EXISTS
                opened_duration UInt64
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_calls_1
            ADD COLUMN IF NOT EXISTS
                opened_count UInt64
        ');
        $this->getClient()->write('
            ALTER TABLE
                dashboard_statistics_calls_2
            ADD COLUMN IF NOT EXISTS
                opened_count UInt64
        ');
    }
}
