<?php

namespace Clickhouse\Migrations;

class Version20230308092753 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                team_id
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                team_id Nullable(UInt128) DEFAULT NULL
            AFTER agent_id
        ');
    }
}
