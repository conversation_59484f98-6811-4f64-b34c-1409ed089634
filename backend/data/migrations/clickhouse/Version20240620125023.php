<?php

namespace Clickhouse\Migrations;

class Version20240620125023 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE
                ems_data_sets
        ');
        $this->getClient()->write(
            '
                CREATE TABLE ems_data_sets
                (
                    `data_set_id` String,
                    `company_id` UInt32,
                    `user_id` UInt128,
                    `event_id` Nullable(UInt64),
                    `name` String,
                    `guideline` String,
                    `status` String,
                    `is_deleted` Bool DEFAULT 0,
                    `created_at` DateTime DEFAULT now(),
                    `updated_at` DateTime DEFAULT now()
                )
                ENGINE = ReplacingMergeTree
                ORDER BY
                    data_set_id
            '
        );
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE
                ems_data_sets
        ');
        $this->getClient()->write(
            '
                CREATE TABLE ems_data_sets
                (
                    `ems_data_set_id` String,
                    `company_id` UInt32,
                    `user_id` UInt128,
                    `event_id` Nullable(UInt64),
                    `name` String,
                    `guideline` String,
                    `status` String,
                    `is_deleted` Bool DEFAULT 0,
                    `created_at` DateTime DEFAULT now(),
                    `updated_at` DateTime DEFAULT now()
                )
                ENGINE = ReplacingMergeTree
                ORDER BY
                    ems_data_set_id
            '
        );
    }
}
