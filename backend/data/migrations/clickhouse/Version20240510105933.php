<?php

namespace Clickhouse\Migrations;

class Version20240510105933 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_reviewed_clients_1
        ');
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_reviewed_clients_2
        ');

        $this->getClient()->write('
            CREATE TABLE dashboard_statistics_reviewed_clients_1 (
                company_id UInt32,
                call_date Date,
                role_id Nullable(UInt32) DEFAULT NULL,
                team_ids Array(UInt32),
                client_id String,
                is_analyzed_count UInt32,
                is_reviewed_count UInt32,
                count UInt32,
            )
            ENGINE = MergeTree()
            ORDER BY (
                company_id,
                call_date
            )
        ');
        $this->getClient()->write('
            CREATE TABLE dashboard_statistics_reviewed_clients_2 (
                company_id UInt32,
                call_date Date,
                role_id Nullable(UInt32) DEFAULT NULL,
                team_ids Array(UInt32),
                client_id String,
                is_analyzed_count UInt32,
                is_reviewed_count UInt32,
                count UInt32,
            )
            ENGINE = MergeTree()
            ORDER BY (
                company_id,
                call_date
            )
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_reviewed_clients_2
        ');
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_reviewed_clients_1
        ');

        $this->getClient()->write('
            CREATE TABLE dashboard_statistics_reviewed_clients_1 (
                company_id UInt32,
                call_date Date,
                role_id UInt32,
                team_ids Array(UInt32),
                reviewed_count UInt32,
                partly_reviewed_count UInt32,
                not_reviewed_count UInt32,
                not_analyzed_count UInt32,
                created_at DateTime
            )
            ENGINE = MergeTree()
            ORDER BY (
                company_id,
                call_date,
                role_id
            )
        ');
        $this->getClient()->write('
            CREATE TABLE dashboard_statistics_reviewed_clients_2 (
                company_id UInt32,
                call_date Date,
                role_id UInt32,
                team_ids Array(UInt32),
                reviewed_count UInt32,
                partly_reviewed_count UInt32,
                not_reviewed_count UInt32,
                not_analyzed_count UInt32,
                created_at DateTime
            )
            ENGINE = MergeTree()
            ORDER BY (
                company_id,
                call_date,
                role_id
            )
        ');
    }
}
