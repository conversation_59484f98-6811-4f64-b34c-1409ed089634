<?php

namespace Clickhouse\Migrations;

class Version20230309110201 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            DROP TABLE IF EXISTS
                event_happenings_changes
        ');
        $this->getClient()->write('
            CREATE TABLE event_happenings_changes (
                company_id UInt32,
                call_id String,
                paragraph_number UInt16,
                role_id UInt32,
                original_event_id UInt32 DEFAULT 0,
                corrected_event_id UInt32,
                user_id UInt32,
                created DateTime DEFAULT now()
            )
            ENGINE = MergeTree()
            PARTITION BY toYYYYMM(created)
            ORDER BY (
                company_id,
                call_id,
                paragraph_number,
                original_event_id,
                corrected_event_id
            )
            SETTINGS index_granularity = 8192
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE IF EXISTS
                event_happenings_changes
        ');
        $this->getClient()->write('
            CREATE TABLE event_happenings_changes (
                company_id UInt32,
                call_id String,
                paragraph_number UInt16,
                role_id UInt32,
                event_id UInt32,
                user_id UInt32,
                deleted Bool DEFAULT 0,
                confirmed Bool DEFAULT 0,
                created DateTime DEFAULT now()
            )
            ENGINE = MergeTree()
            PARTITION BY toYYYYMM(created)
            ORDER BY (
                company_id,
                call_id,
                paragraph_number,
                event_id
            )
            SETTINGS index_granularity = 8192
        ');
    }
}
