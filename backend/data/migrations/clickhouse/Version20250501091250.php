<?php

namespace Clickhouse\Migrations;

class Version20250501091250 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     *
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write(
            '
            ALTER TABLE
                calls
            DROP COLUMN IF EXISTS sourced_original_file_names
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls
            DROP COLUMN IF EXISTS is_emotional_event_analyzed
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN IF EXISTS topic_id
            SETTINGS max_execution_time=30000
        '
        );
        $this->getClient()->write(
            '
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN IF EXISTS topic_name
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN IF EXISTS weight
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN IF EXISTS risk_value
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN IF EXISTS risk_value_sense
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN IF EXISTS risk_value_inner_conflict
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN IF EXISTS risk_value_stress
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls_reviews
            DROP COLUMN IF EXISTS is_approved
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                precalculated_calls
            DROP COLUMN IF EXISTS is_approved
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                precalculated_calls
            DROP COLUMN IF EXISTS risk_value
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                precalculated_calls
            DROP COLUMN IF EXISTS risk_value_sense
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                precalculated_calls
            DROP COLUMN IF EXISTS risk_value_inner_conflict
            SETTINGS max_execution_time=30000
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                precalculated_calls
            DROP COLUMN IF EXISTS risk_value_stress
            SETTINGS max_execution_time=30000
        '
        );
    }

    /**
     *
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write(
            '
            ALTER TABLE
                calls
            ADD COLUMN
                sourced_original_file_names Array(String) DEFAULT [],
            ADD COLUMN
                is_emotional_event_analyzed Bool DEFAULT 0
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls_paragraphs
            ADD COLUMN
                topic_id UInt32 DEFAULT 0,
            ADD COLUMN
                topic_name String DEFAULT \'\',
            ADD COLUMN
                weight Int32 DEFAULT 0,
            ADD COLUMN
                risk_value UInt16 DEFAULT 0,
            ADD COLUMN
                risk_value_sense UInt16 DEFAULT 0,
            ADD COLUMN
                risk_value_inner_conflict UInt16 DEFAULT 0,
            ADD COLUMN
                risk_value_stress UInt16 DEFAULT 0
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                calls_reviews
            ADD COLUMN
                is_approved Nullable(Bool) DEFAULT NULL
            AFTER
                is_reviewed
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                is_approved Nullable(Bool) DEFAULT NULL
            AFTER
                is_reviewed
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                risk_value Float32 DEFAULT 0
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                risk_value_sense Float32 DEFAULT 0
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                risk_value_inner_conflict Float32 DEFAULT 0
        '
        );

        $this->getClient()->write(
            '
            ALTER TABLE
                precalculated_calls
            ADD COLUMN
                risk_value_stress Float32 DEFAULT 0
        '
        );
    }
}
