<?php

namespace Clickhouse\Migrations;

class Version20221104103109 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            RENAME TABLE algo_events TO calls_algo_events
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            RENAME TABLE calls_algo_events TO algo_events
        ');
    }
}
