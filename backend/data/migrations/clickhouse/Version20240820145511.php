<?php

namespace Clickhouse\Migrations;

class Version20240820145511 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                file_path
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                file_path Nullable(String) DEFAULT NULL
                AFTER client_id
        ');
    }
}
