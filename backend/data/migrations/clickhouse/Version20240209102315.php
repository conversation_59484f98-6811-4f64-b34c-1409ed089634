<?php

namespace Clickhouse\Migrations;

class Version20240209102315 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        foreach ([1,2] as $tableIndex) {
            $this->getClient()->write('
                CREATE TABLE dashboard_statistics_calls_languages_' . $tableIndex . ' (
                    company_id UInt32,
                    call_date Date,
                    team_ids Array(UInt32),
                    language String,
                    calls_count UInt32,
                    created_at DateTime
                )
                ENGINE = MergeTree()
                ORDER BY (
                    company_id,
                    call_date
                )
            ');
            $this->getClient()->write('
                CREATE TABLE dashboard_statistics_calls_' . $tableIndex . ' (
                    company_id UInt32,
                    call_date Date,
                    role_id UInt32,
                    team_ids Array(UInt32),
                    calls_count UInt32,
                    calls_duration UInt64,
                    comments_count UInt32,
                    reviewed_count UInt32,
                    analyzed_count UInt32,
                    effective_count UInt32,
                    opened_count UInt32,
                    reviewed_duration UInt64,
                    analyzed_duration UInt64,
                    effective_duration UInt64,
                    opened_duration UInt64,
                    created_at DateTime
                )
                ENGINE = MergeTree()
                ORDER BY (
                    company_id,
                    call_date,
                    role_id
                )
            ');

            $this->getClient()->write('
                CREATE TABLE dashboard_statistics_reviewed_clients_' . $tableIndex . ' (
                    company_id UInt32,
                    call_date Date,
                    role_id UInt32,
                    team_ids Array(UInt32),
                    calls_count UInt32,
                    reviewed_count UInt32,
                    partly_reviewed_count UInt32,
                    not_reviewed_count UInt32,
                    not_analyzed_count UInt32,
                    created_at DateTime
                )
                ENGINE = MergeTree()
                ORDER BY (
                    company_id,
                    call_date,
                    role_id
                )
            ');

            $this->getClient()->write('
                CREATE TABLE dashboard_statistics_managers_kpi_' . $tableIndex . ' (
                    company_id UInt32,
                    reviewed_date Date,
                    role_id UInt32,
                    team_ids Array(UInt32),
                    manager_name String,
                    unique_clients_count UInt64,
                    comments_left UInt32,
                    calls_count UInt64,
                    partly_reviewed_count UInt64,
                    fully_reviewed_count UInt64,
                    calls_duration UInt64,
                    created_at DateTime
                )
                ENGINE = MergeTree()
                ORDER BY (
                    company_id,
                    reviewed_date,
                    role_id
                )
            ');

            $this->getClient()->write('
                CREATE TABLE dashboard_statistics_events_' . $tableIndex . ' (
                    company_id UInt32,
                    call_date Date,
                    role_id UInt32,
                    team_ids Array(UInt32),
                    category_name String,
                    event_name String,
                    fill_color_hex String,
                    outline_color_hex String,
                    calls_count UInt64,
                    moved_from_neutral_count UInt64,
                    moved_to_neutral_count UInt64,
                    changed_from_other_count UInt64,
                    changed_to_other_count UInt64,
                    confirmed_count UInt64,
                    created_at DateTime
                )
                ENGINE = MergeTree()
                ORDER BY (
                    company_id,
                    call_date,
                    role_id
                )
            ');
        }

    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_calls_1
        ');
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_calls_2
        ');
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_events_1
        ');
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_events_2
        ');
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_managers_kpi_1
        ');
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_managers_kpi_2
        ');
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_reviewed_clients_1
        ');
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_reviewed_clients_2
        ');
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_calls_languages_1
        ');
        $this->getClient()->write('
            DROP TABLE dashboard_statistics_calls_languages_2
        ');
    }
}
