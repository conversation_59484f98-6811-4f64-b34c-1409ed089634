<?php

namespace Clickhouse\Migrations;

class Version20221104101405 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                algo_events
            ADD COLUMN
                algo_api_category_id UInt16 DEFAULT 1
            AFTER algo_api_id
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                algo_events
            DROP COLUMN
                algo_api_category_id
        ');
    }
}
