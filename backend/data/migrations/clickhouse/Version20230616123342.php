<?php

namespace Clickhouse\Migrations;

class Version20230616123342 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            RENAME TABLE calls_precalculated TO precalculated_calls
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            RENAME TABLE precalculated_calls TO calls_precalculated
        ');
    }
}
