<?php

namespace Clickhouse\Migrations;

class Version20230906091518 extends \STClickhouse\Entity\Migration\BaseMigration
{
    private const TABLE = 'calls';

    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                ' . self::TABLE . '
            ADD COLUMN
                swap_speakers Bool DEFAULT 0
            AFTER
                is_sent_to_transcribing
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                ' . self::TABLE . '
            DROP COLUMN
                swap_speakers
        ');
    }
}
