<?php

namespace Clickhouse\Migrations;

class Version20230417123653 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            INSERT INTO
                clients
                (
                    company_id,
                    client_id,
                    client_name,
                    status,
                    source,
                    value
                )
            SELECT
            DISTINCT 
                company_id,
                client_id,
                client_name,
                client_status,
                client_source,
                client_value
            FROM
                calls FINAL
                
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        // There is no down for this migration
    }
}
