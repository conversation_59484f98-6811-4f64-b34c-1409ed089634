<?php

namespace STLib\Mvc\DependencyInjection;

class DefaultFactory implements \Laminas\ServiceManager\Factory\FactoryInterface {
    
    /**
     * 
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return AppsflyerService
     */
    public function createService(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): mixed {
        return $this($container, $requestedName ?: PatternPluginManager::class, $this->creationOptions);
    }
    
    /**
     * 
     * @param \Interop\Container\ContainerInterface $container
     * @param string $requestedName
     * @param array $options
     * @return AppsflyerService
     */
    public function __invoke(\Interop\Container\ContainerInterface $container, $requestedName, array $options = null): mixed {
        $arguments = [];
        $class = new \ReflectionClass($requestedName);
        $constructor = $class->getConstructor();
        foreach ($constructor?->getParameters() ?? [] as $parameter) {
            $arguments[] = $container->get($parameter->getType()->getName());
        }
        return new $requestedName(...$arguments);
    }
    
}
