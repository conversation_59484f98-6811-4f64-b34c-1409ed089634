<?php

namespace STLib\Mvc\Command;

use Laminas\Mvc\Controller\PluginManager;
use Lam<PERSON>\ServiceManager\ServiceManager;
use Laminas\Stdlib\DispatchableInterface;
use Laminas\Cli\Command\AbstractParamAwareCommand;
use STCodeManagement\Service\AirbrakeService;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;

abstract class AbstractCommand extends AbstractParamAwareCommand implements DispatchableInterface {

    /**
     * @var PluginManager
     */
    protected ?PluginManager $plugins = null;

    /**
     * 
     * @var \Laminas\ServiceManager\ServiceManager
     */
    protected ?\Laminas\ServiceManager\ServiceManager $serviceManager = null;

    /**
     * 
     * @return PluginManager
     */
    public function getPluginManager(): PluginManager {
        if (!$this->plugins) {
            $this->setPluginManager(new PluginManager(new ServiceManager()));
            $this->plugins->setController($this);
        }
        return $this->plugins;
    }

    /**
     * 
     * @param PluginManager $plugins
     * @return AbstractCommand
     */
    public function setPluginManager(PluginManager $plugins): AbstractCommand {
        $this->plugins = $plugins;
        $this->plugins->setController($this);
        return $this;
    }

    /**
     * 
     * @param string $name
     * @param array $options
     * @return mixed
     */
    public function plugin(string $name, array $options = null): mixed {
        return $this->getPluginManager()->get($name, $options);
    }

    /**
     * 
     * @param string $method
     * @param array $params
     * @return mixed
     */
    public function __call(string $method, array $params = []): mixed {
        $plugin = $this->plugin($method);
        if (is_callable($plugin)) {
            return call_user_func_array($plugin, $params);
        }

        return $plugin;
    }

    /**
     * 
     * @return ServiceManager
     */
    public function getServiceManager(): ServiceManager {
        return $this->serviceManager;
    }

    /**
     * 
     * @param ServiceManager $serviceManager
     * @return AbstractCommand
     */
    public function setServiceManager(ServiceManager $serviceManager): AbstractCommand {
        $this->serviceManager = $serviceManager;
        return $this;
    }
    
    /**
     * 
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws Throwable
     */
    protected function execute(InputInterface $input, OutputInterface $output): int {
        try {
            return $this->launch($input, $output);
        } catch (Throwable $e) {
            $this->getServiceManager()->get(AirbrakeService::class)->register($e);
            $this->roboTruck()->exceptionCollector()->collect($e);

            throw $e;
        }
    }
    
    /**
     * 
     * @param RequestInterface $request
     * @param ResponseInterface|null $response
     */
    public function dispatch(\Laminas\Stdlib\RequestInterface $request, ?\Laminas\Stdlib\ResponseInterface $response = null) {
        throw new \Exception('Command can not be dispatched');
    }
    
    /**
     * 
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws Throwable
     */
    abstract protected function launch(InputInterface $input, OutputInterface $output): int;

}
