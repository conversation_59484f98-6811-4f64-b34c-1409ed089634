<?php

namespace STLib\Db;

use STLib\Db\TableGateway\TableGateway;

abstract class AbstractTable {
    
    /**
     * 
     * @var TableGateway|null
     */
    protected ?TableGateway $tableGateway = null;

    /**
     * 
     * @param TableGateway $tableGateway
     */
    public function __construct(TableGateway $tableGateway) {
        $this->tableGateway = $tableGateway;
    }
    
    /**
     * 
     * @return string
     */
    public function getTable(): string {
        return $this->tableGateway->getTable();
    }
    
    /**
     * 
     * @param string|\Laminas\Db\Sql\TableIdentifier|array $table
     * @return AbstractTable
     */
    public function setTable(string|\Laminas\Db\Sql\TableIdentifier|array $table): AbstractTable {
        $this->tableGateway->setTable($table);
        return $this;
    }
    
    /**
     * 
     * @return \Laminas\Db\Adapter\AdapterInterface
     */
    public function getAdapter(): \Laminas\Db\Adapter\AdapterInterface {
        return $this->tableGateway->getAdapter();
    }
    
    /**
     * 
     * @return \Laminas\Db\Sql\Select
     */
    public function select(): \Laminas\Db\Sql\Select {
        return new \Laminas\Db\Sql\Select($this->getTable());
    }

    /**
     * 
     * Use INSERT ... ON DUPLICATE KEY UPDATE Syntax
     * @since mysql 5.1
     * @param array $insertData For insert array('field_name' => 'field_value')
     * @param array $updateData For update array('field_name' => 'field_value_new')
     * @return boolean
     */
    protected function insertOrUpdate(array $insertData, array $updateData = []): bool {
        $sqlStringTemplate = 'INSERT INTO %s (%s) VALUES (%s) ON DUPLICATE KEY UPDATE %s';
        $adapter = $this->getAdapter();
        $driver = $adapter->getDriver();
        $platform = $adapter->getPlatform();
        $tableName = $this->getTable();
        
        $parameterContainer = new \Laminas\Db\Adapter\ParameterContainer();
        $statementContainer = $adapter->createStatement();
        $statementContainer->setParameterContainer($parameterContainer);

        /* Preparation insert data */
        $insertQuotedValue = [];
        $insertQuotedColumns = [];
        foreach ($insertData as $column => $value) {
            $insertQuotedValue[] = $driver->formatParameterName($column);
            $insertQuotedColumns[] = $platform->quoteIdentifier($column);
            $parameterContainer->offsetSet($column, $value);
        }

        /* Preparation update data */
        $updateQuotedValue = [];
        foreach ($updateData as $column => $value) {
            $updateQuotedValue[] = $platform->quoteIdentifier($column) . '=' . $driver->formatParameterName('update_' . $column);
            $parameterContainer->offsetSet('update_' . $column, $value);
        }
        
        if (empty($updateQuotedValue)) {
            $updateQuotedValue[] = array_key_first($insertData) . '=' . array_key_first($insertData);
        }

        /* Preparation sql query */
        $query = sprintf(
            $sqlStringTemplate, $tableName, implode(',', $insertQuotedColumns), implode(',', array_values($insertQuotedValue)), implode(',', $updateQuotedValue)
        );

        $statementContainer->setSql($query);
        return $statementContainer->execute()->valid();
    }
    
    /**
     * 
     * @param array $data
     * @param bool $useTransaction
     * @return int
     */
    protected function multiInsert(array $data, bool $useTransaction = true): int {
        return $this->multiInsertOrUpdate($data, [], $useTransaction);
    }
    
    /**
     * 
     * @param array $data
     * @param array $updatedColumns
     * @param bool $useTransaction
     * @return int
     * @throws \Exception
     */
    protected function multiInsertOrUpdate(array $data, array $updatedColumns = [], bool $useTransaction = true): int {
        if (count($data) > 0) {
            $columns = array_keys((array)current($data));
            $columnsCount = count($columns);
            $platform = $this->getAdapter()->getPlatform();
            array_walk($columns, function (&$item) use ($platform) {
                $item = $platform->quoteIdentifier($item);
            });
            $columns = '(' . implode(',', $columns) . ')';

            $placeholder = array_fill(0, $columnsCount, '?');
            $placeholder = '(' . implode(',', $placeholder) . ')';
            $placeholder = implode(',', array_fill(0, count($data), $placeholder));

            $values = [];
            foreach ($data as $row) {
                foreach ($row as $value) {
                    $values[] = $value;
                }
            }

            $table = $platform->quoteIdentifier($this->getTable());
            $query = 'INSERT INTO ' . $table . ' ' . $columns . ' VALUES ' . $placeholder;
            if (count($updatedColumns) > 0) {
                $query .= ' ON DUPLICATE KEY UPDATE ';
                $updatedColumnsValues = [];
                foreach ($updatedColumns as $updatedColumn) {
                    $updatedColumnsValues[] = $updatedColumn . ' = VALUES(' . $updatedColumn . ')';
                }
                $query .= implode(', ', $updatedColumnsValues);
            }
            if ($useTransaction) {
                $this->beginTransaction();
                try {
                    $this->getAdapter()->query($query, $values);
                    $this->commit();
                } catch (\Exception $e) {
                    $this->rollback();
                    throw $e;
                }
            } else {
                $this->getAdapter()->query($query, $values);
            }
        }
        return count($data);
    }

    /**
     * 
     * @return void
     */
    public function beginTransaction(): void {
        $this->getAdapter()->getDriver()->getConnection()->beginTransaction();
    }

    /**
     * 
     * @return void
     */
    public function commit(): void {
        $this->getAdapter()->getDriver()->getConnection()->commit();
    }

    /**
     * 
     * @return void
     */
    public function rollback(): void {
        $this->getAdapter()->getDriver()->getConnection()->rollback();
    }
    
}
