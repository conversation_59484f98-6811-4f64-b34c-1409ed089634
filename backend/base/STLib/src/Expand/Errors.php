<?php

namespace STLib\Expand;

trait Errors {

    /**
     *
     * @var array
     */
    protected array $errors = [];

    /**
     *
     * @return array
     */
    public function getErrors(): array {
        if (count($this->errors)) {
            $errors = [];
            foreach ($this->errors as $groupName => $groupErrors) {
                foreach ($groupErrors as $error) {
                    $errors[$groupName][] = $error;
                }
            }
            return $errors;
        }

        return $this->errors;
    }

    /**
     * 
     * @param array $errors
     * @return $this
     */
    public function setErrors(array $errors) {
        $this->errors = $errors;
        return $this;
    }

    /**
     * 
     * @param string $attribute
     * @return bool
     */
    public function hasError(string $attribute): bool {
        return array_key_exists(strtolower($attribute), $this->errors);
    }

    /**
     * 
     * @return bool
     */
    public function baseValidate(): bool {
        return !!$this->getErrors();
    }

    /**
     * 
     * @return bool
     */
    public function validate(): bool {
        return $this->baseValidate();
    }

}