<?php

/**
 * @see       https://github.com/laminas/laminas-mvc-skeleton for the canonical source repository
 * @copyright https://github.com/laminas/laminas-mvc-skeleton/blob/master/COPYRIGHT.md
 * @license   https://github.com/laminas/laminas-mvc-skeleton/blob/master/LICENSE.md New BSD License
 */

/**
 * List of enabled modules for this application.
 *
 * This should be an array of module namespaces used in the application.
 */
return [
    '<PERSON>inas\Mvc\Middleware',
    'Laminas\Mvc\I18n',
    'Laminas\I18n',
    'Laminas\Di',
    'Laminas\Log',
    'Lam<PERSON>\Db',
    '<PERSON><PERSON>\Mvc\Plugin\FilePrg',
    'Laminas\Form',
    'Laminas\Hydrator',
    'Laminas\InputFilter',
    'Laminas\Filter',
    'Laminas\Mvc\Plugin\FlashMessenger',
    'Laminas\Mvc\Plugin\Identity',
    '<PERSON><PERSON>\Mvc\Plugin\Prg',
    '<PERSON><PERSON>\Session',
    '<PERSON><PERSON>\Router',
    '<PERSON><PERSON>\Validator',
    '<PERSON><PERSON>\Diactoros',
    'STLib',
    'STClickhouse',
    'STCodeManagement',
    'STConfiguration',
    'STRedis',
    'STLog',
    'STMail',
    'STSlack',
    'STChatGPT',
    'STRabbit',
    'STUser',
    'STAlgo',
    'STCompany',
    'STCall',
    'STReport',
    'STDashboard',
    'STRoboMetrics',
    'STS3Report',
    'STRoboTruck',
    'STEms',
    'STApi',
    'STFront',
    'STLlmEvent',
    'STIndustry',
    'STOnboarding',
    'STTranslation',
    'Application',
    'Console',
    'Api',
];
